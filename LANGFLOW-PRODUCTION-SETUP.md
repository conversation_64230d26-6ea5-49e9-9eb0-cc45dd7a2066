# 🚀 Langflow Production Setup - Complete Guide

## 📋 Overview

This guide provides everything you need to deploy IMTA AI Chat with real Langflow API integration instead of mock responses.

## ✅ **What Has Been Configured**

### 1. **Production-Ready Code Changes**
- ✅ **Re-enabled Langflow API** in environment configuration
- ✅ **Modified streaming endpoint** to prioritize real API over mock responses
- ✅ **Added production mode detection** (`NODE_ENV=production`)
- ✅ **Enhanced error handling** with proper fallback strategies
- ✅ **Improved logging** for production monitoring

### 2. **Configuration Files Updated**
- ✅ **`server/.env`** - Template with placeholder credentials
- ✅ **`server/.env.production`** - Complete production environment template
- ✅ **`docker-compose.prod.yml`** - Updated with Langflow environment variables
- ✅ **Production deployment guide** created

### 3. **Verification and Setup Tools**
- ✅ **`verify-langflow-api.js`** - API verification script
- ✅ **`setup-production-credentials.js`** - Automated credential setup
- ✅ **`PRODUCTION-DEPLOYMENT-GUIDE.md`** - Comprehensive deployment guide

## 🔧 **Quick Setup Instructions**

### Option 1: Automated Setup (Recommended)
```bash
# Run the automated setup script
node setup-production-credentials.js

# Follow the prompts to enter your valid Langflow credentials
# The script will update all configuration files automatically
```

### Option 2: Manual Setup
```bash
# 1. Update server/.env
LANGFLOW_API_URL=https://your-langflow-instance.com/api/v1/run/YOUR_FLOW_ID
LANGFLOW_API_KEY=YOUR_VALID_API_KEY

# 2. Update docker-compose.prod.yml
- LANGFLOW_API_URL=https://your-langflow-instance.com/api/v1/run/YOUR_FLOW_ID
- LANGFLOW_API_KEY=YOUR_VALID_API_KEY
```

## 🧪 **Verification Steps**

### 1. Verify API Credentials
```bash
cd server
node verify-langflow-api.js
```

**Expected Output:**
```
🎉 Langflow API Verification Completed Successfully!
✅ Environment variables configured
✅ Basic connectivity working
✅ Streaming functionality working
✅ Ready for production deployment
```

### 2. Test Local Development
```bash
# Start development server
cd server && npm run dev

# Test chat functionality in browser
# Verify real AI responses (not mock data)
```

## 🚀 **Production Deployment**

### Using Docker (Recommended)
```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d --build

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f app
```

### Manual Deployment
```bash
# Set production environment
export NODE_ENV=production

# Install dependencies
cd server && npm ci --only=production

# Start application
npm start
```

## 🔍 **Production Behavior**

### **In Production Mode (`NODE_ENV=production`):**
- ✅ **Requires valid Langflow API** - no fallback to mock responses
- ✅ **Returns user-friendly errors** when Langflow API fails
- ✅ **Enhanced logging** for monitoring and debugging
- ✅ **Strict error handling** - fails fast if API not configured

### **In Development Mode:**
- ✅ **Falls back to mock responses** when Langflow API fails
- ✅ **Detailed error logging** for debugging
- ✅ **Graceful degradation** for development convenience

## 📊 **Monitoring and Health Checks**

### Application Health
```bash
# Health check endpoint
curl http://your-domain.com/api/health

# Expected response
{"status":"ok","timestamp":"2025-06-19T...","uptime":...}
```

### Chat Functionality Test
```bash
# Test chat endpoint (requires authentication)
curl -X POST http://your-domain.com/api/chat/stream \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"message":"Test message"}'
```

### Log Monitoring
```bash
# Docker logs
docker-compose -f docker-compose.prod.yml logs -f app

# Look for these indicators:
# ✅ "Langflow API configured successfully"
# ✅ "Streaming response from Langflow"
# ❌ "Langflow API error" (indicates API issues)
```

## 🚨 **Troubleshooting**

### Common Issues and Solutions

#### 1. **API Key Invalid (403 Forbidden)**
```
Error: Request failed with status code 403
Detail: "An API key must be passed as query or header"
```

**Solution:**
- Get a new API key from your Langflow provider
- Verify the key format (usually starts with `sk-`)
- Check if the key has proper permissions

#### 2. **Flow Not Found (404 Not Found)**
```
Error: Request failed with status code 404
```

**Solution:**
- Verify your flow ID in the URL
- Ensure the flow is published and accessible
- Check if the flow URL is correct

#### 3. **Mock Responses in Production**
```
Log: "source": "mock-fallback"
```

**Solution:**
- Verify `NODE_ENV=production` is set
- Check Langflow API configuration
- Run verification script to test API

#### 4. **Container Won't Start**
```
Error: Environment variable LANGFLOW_API_KEY is required
```

**Solution:**
- Update docker-compose.prod.yml with valid credentials
- Rebuild containers: `docker-compose -f docker-compose.prod.yml up -d --build`

## 📋 **Pre-Deployment Checklist**

### ✅ **Required Steps**
- [ ] Valid Langflow API URL obtained
- [ ] Valid Langflow API key obtained
- [ ] Credentials updated in configuration files
- [ ] Verification script passes all tests
- [ ] Production environment variables set
- [ ] Docker images build successfully
- [ ] SSL certificates configured (if applicable)

### ✅ **Post-Deployment Verification**
- [ ] Application starts without errors
- [ ] Health check endpoint responds
- [ ] Chat sends real AI responses (not mock)
- [ ] No "mock-fallback" in production logs
- [ ] Authentication system works
- [ ] File upload functionality works
- [ ] All containers healthy (Docker deployment)

## 🔐 **Security Notes**

### **API Key Security**
- ⚠️ **Never commit API keys to version control**
- ✅ **Use environment variables for credentials**
- ✅ **Rotate API keys regularly**
- ✅ **Monitor API key usage and limits**

### **Production Security**
- ✅ **Use HTTPS in production**
- ✅ **Configure proper CORS origins**
- ✅ **Implement rate limiting**
- ✅ **Monitor for suspicious activity**

## 📞 **Support and Next Steps**

### **If You Need Help:**
1. **Check the verification script output** for specific error details
2. **Review the production deployment guide** for comprehensive instructions
3. **Check application logs** for detailed error information
4. **Contact your Langflow provider** for API-related issues

### **After Successful Deployment:**
1. **Monitor application performance** and error rates
2. **Set up automated health checks** and alerting
3. **Plan for API key rotation** and maintenance
4. **Consider implementing multiple AI providers** for redundancy

---

**🎉 You're now ready to deploy IMTA AI Chat with real Langflow AI integration!**

**Last Updated:** June 19, 2025  
**Version:** 1.0.0  
**Prepared by:** IMTA AI Development Team
