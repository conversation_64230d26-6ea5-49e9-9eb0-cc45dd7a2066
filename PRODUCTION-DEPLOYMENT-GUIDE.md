# 🚀 Production Deployment Guide - Langflow API Integration

## 📋 Overview

This guide helps you deploy the IMTA AI Chat system to production with real Langflow AI responses instead of mock data.

## ⚠️ Prerequisites

Before deploying to production, ensure you have:

1. **Valid Langflow API Credentials**
   - Working Langflow API URL
   - Valid API key with proper permissions
   - Published and accessible Langflow flow

2. **Production Environment**
   - Docker and Docker Compose installed
   - Proper domain and SSL certificates
   - Database and storage configured

## 🔧 Step-by-Step Deployment

### Step 1: Update Langflow API Credentials

#### Option A: Update Docker Compose (Recommended)
Edit `docker-compose.prod.yml`:

```yaml
environment:
  # Update these with your valid production credentials
  - LANGFLOW_API_URL=https://your-langflow-instance.com/api/v1/run/YOUR_FLOW_ID
  - LANGFLOW_API_KEY=YOUR_VALID_API_KEY_HERE
```

#### Option B: Update Environment File
Copy production template:
```bash
cp server/.env.production server/.env
```

Then edit `server/.env`:
```env
# Update these values
LANGFLOW_API_URL=https://your-langflow-instance.com/api/v1/run/YOUR_FLOW_ID
LANGFLOW_API_KEY=YOUR_VALID_API_KEY_HERE
```

### Step 2: Verify Langflow API Integration

Run the verification script:
```bash
cd server
node verify-langflow-api.js
```

Expected output:
```
🎉 Langflow API Verification Completed Successfully!
✅ Environment variables configured
✅ Basic connectivity working
✅ Streaming functionality working
✅ Ready for production deployment
```

### Step 3: Deploy to Production

#### Using Docker Compose:
```bash
# Build and start production containers
docker-compose -f docker-compose.prod.yml up -d --build

# Check container status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f app
```

#### Manual Deployment:
```bash
# Set production environment
export NODE_ENV=production

# Install dependencies
cd server && npm ci --only=production

# Start the application
npm start
```

### Step 4: Verify Production Deployment

1. **Health Check:**
   ```bash
   curl http://your-domain.com/api/health
   ```

2. **Test Chat Functionality:**
   - Open your application in browser
   - Send a test message
   - Verify you receive real AI responses (not mock data)

3. **Check Logs:**
   ```bash
   # Docker logs
   docker-compose -f docker-compose.prod.yml logs app

   # Manual deployment logs
   tail -f logs/app.log
   ```

## 🔍 Verification Checklist

### ✅ Pre-Deployment Checklist

- [ ] Langflow API URL is correct and accessible
- [ ] API key is valid and has proper permissions
- [ ] Langflow flow is published and working
- [ ] Environment variables are properly set
- [ ] Verification script passes all tests
- [ ] Docker images build successfully
- [ ] Database connections are configured
- [ ] SSL certificates are in place (if applicable)

### ✅ Post-Deployment Checklist

- [ ] Application starts without errors
- [ ] Health check endpoint responds
- [ ] Chat functionality works with real AI responses
- [ ] No mock responses in production logs
- [ ] File upload functionality works
- [ ] Authentication system works
- [ ] Database operations are successful
- [ ] All containers are healthy (if using Docker)

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Langflow API 403 Forbidden
```
Error: Request failed with status code 403
```

**Solutions:**
- Verify API key is correct and not expired
- Check if API key has proper permissions
- Ensure Langflow flow is published
- Verify API URL is correct

#### 2. Langflow API Timeout
```
Error: timeout of 30000ms exceeded
```

**Solutions:**
- Check Langflow service availability
- Increase timeout in production config
- Verify network connectivity
- Check for rate limiting

#### 3. Mock Responses in Production
```
Log: "source": "mock-fallback"
```

**Solutions:**
- Verify NODE_ENV=production is set
- Check Langflow API configuration
- Review application logs for API errors
- Run verification script

#### 4. Container Startup Issues
```
Error: listen EADDRINUSE: address already in use
```

**Solutions:**
- Stop existing containers: `docker-compose down`
- Check port conflicts: `netstat -tulpn | grep :5001`
- Update port mapping in docker-compose.prod.yml

## 📊 Monitoring and Maintenance

### Production Monitoring

1. **Application Logs:**
   ```bash
   # Monitor real-time logs
   docker-compose -f docker-compose.prod.yml logs -f app
   
   # Check for errors
   docker-compose -f docker-compose.prod.yml logs app | grep ERROR
   ```

2. **Health Checks:**
   ```bash
   # Application health
   curl http://your-domain.com/api/health
   
   # Container health
   docker-compose -f docker-compose.prod.yml ps
   ```

3. **Langflow API Monitoring:**
   ```bash
   # Run periodic verification
   cd server && node verify-langflow-api.js
   ```

### Maintenance Tasks

1. **Regular API Key Rotation:**
   - Update API keys periodically
   - Test new keys before deployment
   - Update both .env and docker-compose files

2. **Log Management:**
   - Rotate application logs
   - Monitor disk space usage
   - Archive old logs

3. **Performance Monitoring:**
   - Monitor response times
   - Check memory and CPU usage
   - Monitor database performance

## 🔐 Security Considerations

### Production Security

1. **Environment Variables:**
   - Never commit API keys to version control
   - Use secure secret management
   - Rotate keys regularly

2. **Network Security:**
   - Use HTTPS in production
   - Configure proper CORS origins
   - Implement rate limiting

3. **Container Security:**
   - Use non-root users in containers
   - Keep base images updated
   - Scan for vulnerabilities

## 📞 Support and Escalation

### When to Escalate

- Langflow API consistently failing
- High error rates in production
- Performance degradation
- Security incidents

### Support Contacts

- **Technical Team:** [Your team contact]
- **Langflow Support:** [Langflow support contact]
- **Infrastructure Team:** [Infrastructure team contact]

---

**Last Updated:** June 19, 2025  
**Version:** 1.0.0  
**Prepared by:** IMTA AI Development Team
