import React, { useState } from 'react';
import { useChat } from '../context/ChatContext';
import { useAuth } from '../context/AuthContext';
import { Bug, Eye, EyeOff } from 'lucide-react';

const DebugPanel = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { conversations, currentConversationId, isLoading, error } = useChat();
  const { isAuthenticated, user } = useAuth();

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-2 sm:bottom-4 right-2 sm:right-4 bg-red-500 text-white p-2 rounded-full shadow-lg hover:bg-red-600 z-30"
        title="Show Debug Panel"
      >
        <Bug className="w-4 h-4 sm:w-5 sm:h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-2 sm:bottom-4 right-2 sm:right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-3 sm:p-4 max-w-xs sm:max-w-md max-h-80 sm:max-h-96 overflow-auto z-30">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-bold text-gray-800 flex items-center gap-2">
          <Bug className="w-4 h-4" />
          Debug Panel
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <EyeOff className="w-4 h-4" />
        </button>
      </div>
      
      <div className="space-y-3 text-sm">
        {/* Authentication State */}
        <div className="border-b border-gray-200 pb-2">
          <strong className="text-blue-600">Authentication:</strong>
          <div className="ml-2 mt-1">
            <div>Status: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>{isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</span></div>
            <div>User: {user ? (user.fullName || user.email) : 'None'}</div>
            <div>Token: {localStorage.getItem('authToken') ? 'Present' : 'Missing'}</div>
          </div>
        </div>

        {/* Chat State */}
        <div className="border-b border-gray-200 pb-2">
          <strong className="text-green-600">Chat Context:</strong>
          <div className="ml-2 mt-1">
            <div>Conversations: {conversations.length}</div>
            <div>Current ID: {currentConversationId || 'None'}</div>
            <div>Loading: <span className={isLoading ? 'text-orange-600' : 'text-gray-600'}>{isLoading ? 'Yes' : 'No'}</span></div>
            <div>Error: <span className={error ? 'text-red-600' : 'text-gray-600'}>{error || 'None'}</span></div>
          </div>
        </div>
        
        <div>
          <strong className="text-purple-600">Conversations List:</strong>
          <div className="mt-1 space-y-1 max-h-32 overflow-y-auto">
            {conversations.length === 0 ? (
              <div className="text-gray-500 text-xs">No conversations</div>
            ) : (
              conversations.map((conv, index) => (
                <div key={conv.id} className="bg-gray-50 p-2 rounded text-xs">
                  <div><strong>#{index + 1}</strong></div>
                  <div><strong>ID:</strong> {conv.id.substring(0, 8)}...</div>
                  <div><strong>Backend ID:</strong> {conv.backendChatId?.substring(0, 8) || 'None'}...</div>
                  <div><strong>Title:</strong> {conv.title}</div>
                  <div><strong>Messages:</strong> {conv.messages?.length || 0}</div>
                  <div><strong>Current:</strong> {conv.id === currentConversationId ? 'Yes' : 'No'}</div>
                </div>
              ))
            )}
          </div>
        </div>
        
        {/* Actions */}
        <div className="space-y-2">
          <button
            onClick={() => {
              console.log('=== DEBUG INFO ===');
              console.log('Auth state:', { isAuthenticated, user });
              console.log('Chat state:', { conversations, currentConversationId, isLoading, error });
              console.log('LocalStorage token:', localStorage.getItem('authToken'));
            }}
            className="w-full bg-blue-500 text-white py-1 px-2 rounded text-xs hover:bg-blue-600"
          >
            Log to Console
          </button>

          <button
            onClick={async () => {
              try {
                const response = await fetch('/api/chat/history?page=1&limit=10', {
                  headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                  }
                });
                const data = await response.json();
                console.log('Manual API test:', data);
              } catch (error) {
                console.error('Manual API test error:', error);
              }
            }}
            className="w-full bg-green-500 text-white py-1 px-2 rounded text-xs hover:bg-green-600"
          >
            Test Chat API
          </button>

          <button
            onClick={() => {
              localStorage.clear();
              window.location.reload();
            }}
            className="w-full bg-red-500 text-white py-1 px-2 rounded text-xs hover:bg-red-600"
          >
            Clear localStorage & Reload
          </button>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;
