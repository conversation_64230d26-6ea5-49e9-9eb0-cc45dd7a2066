import React, { useState, useEffect } from 'react';
import { User, Bot, Download, Eye, FileText, Image, Video, Music, Archive, File } from 'lucide-react';
import ChartDisplay from './ChartDisplay';
import TypewriterText from './TypewriterText';
import StreamingText from './StreamingText';
import TypewriterStreamingText from './TypewriterStreamingText';
import ImageDisplay from './ImageDisplay';
import { parseContentWithImages } from '../utils/imageDetector';

// Simple Markdown Component
const MarkdownContent = ({ content }) => {  const renderMarkdown = (text) => {
    // Clean up any typing indicators and control characters that might be in the content
    const cleanText = text
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();
    
    // Simple markdown parsing for common elements
    let html = cleanText
      // Headers
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-bold mt-4 mb-3">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-4 mb-3">$1</h1>')
      // Bold and italic
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-3 rounded mt-2 mb-2 text-sm overflow-x-auto"><code>$1</code></pre>')
      .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>')
      // Lists
      .replace(/^- (.*$)/gm, '<li class="ml-4">• $1</li>')
      .replace(/^(\d+)\. (.*$)/gm, '<li class="ml-4">$1. $2</li>')
      // Blockquotes
      .replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-gray-300 pl-4 py-2 my-2 bg-gray-50 italic">$1</blockquote>')
      // Line breaks
      .replace(/\n\n/g, '</p><p class="mb-2">')
      .replace(/\n/g, '<br/>');

    // Wrap in paragraph if not already wrapped
    if (!html.includes('<h1>') && !html.includes('<h2>') && !html.includes('<h3>') && !html.includes('<p>')) {
      html = `<p class="mb-2">${html}</p>`;
    }

    return html;
  };

  return (
    <div 
      className="markdown-content"
      dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
    />
  );
};

const Message = ({ message, enableTyping = true, enableStreaming = true }) => {
  const isUser = message.role === 'user';
  const isBot = message.role === 'assistant';
  const [parsedContent, setParsedContent] = useState({ text: '', images: [] });

  const { content, role, isStreaming, type = 'text', metadata, files, hasFiles, timestamp } = message;

  // Don't render if message has no content and is not streaming
  if (!content && !isStreaming) {
    return null;
  }

  const formatTime = (ts) => {
    if (!ts) return '';
    return new Date(ts).toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Parse content for images
  useEffect(() => {
    if (content) {
      const parsed = parseContentWithImages(content);
      setParsedContent(parsed);
    }
  }, [content]);  const renderContent = () => {
    // Don't clean content for streaming - keep original text but clean control characters
    const originalContent = content || '';
    const cleanContent = originalContent
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();
    const cleanParsedText = (parsedContent?.text || '')
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();
    
    if (isBot && isStreaming) {
      // Use TypewriterStreamingText for streaming effect with cursor
      return (
        <TypewriterStreamingText
          text={originalContent || 'Đang phản hồi...'}
          speed={30}
          showCursor={true}
          cursorChar="|"
          className="streaming-content"
        />
      );
    } else if (type === 'markdown' || (isBot && !isStreaming)) {
      // For completed bot messages, always use MarkdownContent (no streaming animation)
      return <MarkdownContent content={cleanParsedText || cleanContent} />;
    } else if (type === 'chart') {
      try {
        const chartData = typeof cleanContent === 'string' ? JSON.parse(cleanContent) : cleanContent;
        return <ChartDisplay data={chartData} />;
      } catch (e) {
        console.error("Error parsing chart data:", e);
        return <p className="text-red-500">Lỗi hiển thị biểu đồ.</p>;
      }
    } else if (parsedContent?.images?.length > 0 && !isUser) {
        return (
          <>
            {cleanParsedText && <p className="mb-2">{cleanParsedText}</p>}
            {parsedContent.images.map((img, idx) => (
              <ImageDisplay key={idx} src={img.url} alt={img.alt || `Uploaded Image ${idx + 1}`} />
            ))}
          </>
        );
    }
    // Default to plain text
    return <p>{cleanParsedText || cleanContent}</p>; 
  };

  // Helper function to get file icon based on type
  const getFileIcon = (file) => {
    const mimeType = file.mimeType || file.type || '';
    const fileName = file.name || file.originalName || '';

    if (mimeType.startsWith('image/')) return Image;
    if (mimeType.startsWith('video/')) return Video;
    if (mimeType.startsWith('audio/')) return Music;
    if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return FileText;
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) return Archive;

    // Fallback based on file extension
    const ext = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) return Image;
    if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext)) return Video;
    if (['mp3', 'wav', 'ogg', 'flac'].includes(ext)) return Music;
    if (['pdf', 'doc', 'docx', 'txt'].includes(ext)) return FileText;
    if (['zip', 'rar', '7z', 'tar'].includes(ext)) return Archive;

    return File;
  };

  // Helper function to format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const renderFiles = () => {
    if (!hasFiles || !files || files.length === 0) return null;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs font-medium text-gray-500 mb-2">📎 Tệp đính kèm ({files.length}):</p>
        <div className="space-y-2">
          {files.map((file, index) => {
            const IconComponent = getFileIcon(file);
            const fileName = file.name || file.originalName || `File ${index + 1}`;
            const fileSize = formatFileSize(file.size);
            const downloadUrl = file.downloadUrl || file.url;
            const viewUrl = file.viewUrl || file.url;

            return (
              <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <IconComponent className="w-4 h-4 text-gray-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate" title={fileName}>
                    {fileName}
                  </p>
                  <p className="text-xs text-gray-500">
                    {fileSize} • {file.mimeType || 'Unknown type'}
                  </p>
                </div>
                <div className="flex-shrink-0 flex gap-1">
                  {/* View button for images and PDFs */}
                  {(file.mimeType?.startsWith('image/') || file.mimeType?.includes('pdf')) && viewUrl && (
                    <button
                      onClick={() => window.open(viewUrl, '_blank')}
                      className="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded"
                      title="Xem file"
                    >
                      <Eye className="w-3 h-3" />
                    </button>
                  )}
                  {/* Download button */}
                  {downloadUrl && (
                    <button
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = downloadUrl;
                        link.download = fileName;
                        link.target = '_blank';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                      className="p-1 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded"
                      title="Tải xuống"
                    >
                      <Download className="w-3 h-3" />
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={`
      flex gap-2 sm:gap-3 p-2 sm:p-4
      ${isUser ? 'justify-end' : 'justify-start'}
      animate-fade-in-up message-container
      ${role} ${isStreaming ? 'streaming' : ''} // Added for CSS targeting from chatstream.md
    `}>
      {/* Avatar */}
      {isBot && (
        <div className="flex-shrink-0">
          <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <Bot className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
          </div>
        </div>
      )}

      {/* Message Bubble */}
      <div className={`
        max-w-xs sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl
        p-3 sm:p-4 rounded-xl shadow-sm message-bubble
        ${isUser ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-100 text-gray-800 rounded-bl-none'}
      `}>
        <div className="message-content">
          {renderContent()}
        </div>
        {renderFiles()} 
        <div className="text-xs mt-2 flex justify-between items-center message-metadata">
          <span className={`${isUser ? 'text-blue-200' : 'text-gray-500'}`}>
            {formatTime(timestamp || metadata?.timestamp)} 
          </span>
           {isBot && !isStreaming && metadata?.completedAt && (
            <span className="text-gray-400 text-xxs ml-2">Hoàn tất</span>
          )}
        </div>
      </div>

      {/* User Avatar */}
      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          </div>
        </div>
      )}
    </div>
  );
};

export default Message;

// TODO: Add back memo optimization later after fixing streaming issues

// Memoization to prevent unnecessary re-renders of completed messages

// Basic CSS for streaming effects (can be moved to a .css file)
// Ensure these are added to your global CSS or a relevant CSS module if not already present
/*
.message.streaming .message-content {
  opacity: 0.9; // Example: slightly transparent while streaming
}

.streaming-content .streaming-cursor {
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.message-metadata {
  font-size: 0.75em; // Adjusted for potentially smaller text
  color: #666;
  margin-top: 4px;
}
*/
