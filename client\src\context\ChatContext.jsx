import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { chatAPI } from '../utils/api';
import { analyzeChatContext } from '../utils/chatAnalyzer';
import { useAuth } from './AuthContext';

// Initial state
const initialState = {
  conversations: [],
  currentConversationId: null,
  isLoading: false,
  error: null,
};

// Action types
const actionTypes = {
  SET_CONVERSATIONS: 'SET_CONVERSATIONS',
  ADD_CONVERSATION: 'ADD_CONVERSATION',
  UPDATE_CONVERSATION: 'UPDATE_CONVERSATION',
  SET_CURRENT_CONVERSATION: 'SET_CURRENT_CONVERSATION',
  ADD_MESSAGE: 'ADD_MESSAGE',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  DELETE_CONVERSATION: 'DELETE_CONVERSATION',
  UPDATE_MESSAGE: 'UPDATE_MESSAGE',
  CLEAR_CONVERSATIONS: 'CLEAR_CONVERSATIONS',
};

// Reducer function
const chatReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_CONVERSATIONS:
      return {
        ...state,
        conversations: action.payload,
      };

    case actionTypes.ADD_CONVERSATION:
      const newConversation = {
        id: action.payload.id || uuidv4(), // Use provided ID or generate new one
        title: action.payload.title || 'Cuộc trò chuyện mới',
        messages: [],
        createdAt: new Date().toISOString(),
        backendChatId: action.payload.backendChatId || null,
        type: action.payload.type || 'general',
        metadata: action.payload.metadata || {}
      };
      return {
        ...state,
        conversations: [newConversation, ...state.conversations],
        currentConversationId: newConversation.id,
      };

    case actionTypes.SET_CURRENT_CONVERSATION:
      return {
        ...state,
        currentConversationId: action.payload,
      };

    case actionTypes.ADD_MESSAGE:
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? {
                ...conv,
                messages: [...conv.messages, action.payload.message],
                title: conv.messages.length === 0 ? action.payload.message.content.substring(0, 50) + '...' : conv.title,
              }
            : conv
        ),
      };

    case actionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };

    case actionTypes.UPDATE_CONVERSATION:
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.id
            ? { ...conv, ...action.payload.updates }
            : conv
        ),
      };

    case actionTypes.DELETE_CONVERSATION:
      const filteredConversations = state.conversations.filter(conv => conv.id !== action.payload);
      return {
        ...state,
        conversations: filteredConversations,
        currentConversationId: state.currentConversationId === action.payload 
          ? (filteredConversations.length > 0 ? filteredConversations[0].id : null)
          : state.currentConversationId,
      };

    case actionTypes.UPDATE_MESSAGE:
      return {
        ...state,
        conversations: state.conversations.map(conv => {
          if (conv.id === action.payload.conversationId) {
            return {
              ...conv,
              messages: conv.messages.map(msg => {
                if (msg.id === action.payload.messageId) {
                  // If updates is a function, pass the current message state
                  const newUpdates = typeof action.payload.updates === 'function'
                    ? action.payload.updates(msg)
                    : action.payload.updates;
                  return { ...msg, ...newUpdates };
                }
                return msg;
              })
            };
          }
          return conv;
        })
      };

    case actionTypes.CLEAR_CONVERSATIONS:
      return {
        ...state,
        conversations: [],
        currentConversationId: null,
      };

    default:
      return state;
  }
};

// Create context
const ChatContext = createContext();

// Provider component
export const ChatProvider = ({ children }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const { isAuthenticated, user } = useAuth();

  // Load conversations from backend when user is authenticated
  useEffect(() => {
    const initializeChats = async () => {
      console.log('🔄 Initializing chats from backend...', { isAuthenticated, user: !!user });

      // Clear conversations if user is not authenticated
      if (!isAuthenticated) {
        console.log('❌ User not authenticated, clearing conversations');
        dispatch({ type: actionTypes.CLEAR_CONVERSATIONS });
        return;
      }

      try {
        // Check if user is authenticated
        const token = localStorage.getItem('authToken');
        if (!token) {
          console.log('❌ No auth token found, skipping backend chat loading');
          return;
        }

        // Try to load from backend first
        console.log('🌐 Loading chat history from backend...');
        const response = await chatAPI.getChatHistory({ page: 1, limit: 50 });
        console.log('✅ Chat history response:', response);
        
        if (response?.data?.chats) {
          const backendChats = response.data.chats.map(chat => ({
            id: uuidv4(), // Generate local ID for UI state management
            backendChatId: chat._id,
            title: chat.title,
            type: chat.type || 'general',
            status: chat.status || 'active',
            metadata: chat.metadata || {},
            messages: [], // Messages will be loaded when chat is selected
            createdAt: chat.createdAt,
            updatedAt: chat.updatedAt || chat.createdAt,
            lastMessageAt: chat.lastMessageAt || chat.createdAt,
            messageCount: chat.analytics?.messageCount || 0
          }));
          
          console.log('✅ Mapped backend chats:', backendChats.length, 'chats');
          
          dispatch({ type: actionTypes.SET_CONVERSATIONS, payload: backendChats });
          // Don't automatically set current conversation - let user start fresh
          // This ensures when they type first message, it will create a new conversation
          console.log('ℹ️ Not setting current conversation - user will start fresh');
          return;
        }
      } catch (error) {
        console.error('❌ Error loading chats from backend:', error);
      }

      // Fallback to localStorage only if backend fails
      console.log('🔄 Fallback to localStorage...');
      const savedConversations = localStorage.getItem('imta-ai-conversations');
      if (savedConversations) {
        const conversations = JSON.parse(savedConversations);
        dispatch({ type: actionTypes.SET_CONVERSATIONS, payload: conversations });
        // Don't automatically set current conversation - let user start fresh
        console.log('ℹ️ Not setting current conversation from localStorage - user will start fresh');
      }
    };

    initializeChats();
  }, [isAuthenticated, user]); // Re-run when authentication state changes

  // Save conversations to localStorage whenever they change
  useEffect(() => {
    if (state.conversations.length > 0) {
      localStorage.setItem('imta-ai-conversations', JSON.stringify(state.conversations));
    }
  }, [state.conversations]);

  // Actions
  const actions = {
    addConversation: async (title = 'Cuộc trò chuyện mới') => {
      console.log('🆕 Creating new conversation:', title);
      
      try {
        // Check if user is authenticated
        const token = localStorage.getItem('authToken');
        if (!token) {
          console.log('❌ No auth token found, creating local-only conversation');
          // Fallback to local-only conversation
          const localId = uuidv4();
          dispatch({ 
            type: actionTypes.ADD_CONVERSATION, 
            payload: { title, id: localId } 
          });
          return;
        }
        
        console.log('🌐 Calling backend createChat API...');
        
        // Create chat on backend immediately
        const chatResponse = await chatAPI.createChat({
          title: title,
          type: 'general',
          metadata: {}
        });
        
        console.log('✅ Backend chat created successfully:', chatResponse);
        
        const backendChatId = chatResponse.data.chat._id;
        const localId = uuidv4();
        
        // Create conversation with backend chat ID
        dispatch({
          type: actionTypes.ADD_CONVERSATION,
          payload: {
            title: chatResponse.data.chat.title,
            id: localId,
            backendChatId: backendChatId,
            type: chatResponse.data.chat.type || 'general',
            metadata: chatResponse.data.chat.metadata || {}
          }
        });
        
        console.log('✅ New conversation created with backend ID:', backendChatId);
        
      } catch (error) {
        console.error('❌ Error creating backend chat:', error);
        console.log('🔄 Fallback to local-only conversation');
        
        // Fallback to local-only conversation
        const localId = uuidv4();
        dispatch({ 
          type: actionTypes.ADD_CONVERSATION, 
          payload: { title, id: localId } 
        });
      }
    },

    setCurrentConversation: async (id) => {
      console.log('🔄 Setting current conversation:', id);
      dispatch({ type: actionTypes.SET_CURRENT_CONVERSATION, payload: id });
      
      // Load messages for the selected conversation if it has a backend chat ID
      const conversation = state.conversations.find(conv => conv.id === id);
      console.log('📋 Found conversation:', conversation);
      
      if (conversation?.backendChatId && conversation.messages.length === 0) {
        console.log('🌐 Loading messages from backend for chat:', conversation.backendChatId);
        
        try {
          dispatch({ type: actionTypes.SET_LOADING, payload: true });
          
          const response = await chatAPI.getChat(conversation.backendChatId);
          console.log('✅ Chat details response:', response);
          
          if (response?.data?.messages) {
            const messages = response.data.messages.map(msg => ({
              id: uuidv4(),
              content: msg.content,
              role: msg.sender === 'user' ? 'user' : 'assistant',
              timestamp: msg.timestamp,
              type: msg.type || 'text',
              files: msg.files || undefined,
              hasFiles: msg.files && msg.files.length > 0
            }));

            console.log('✅ Mapped messages:', messages.length, 'messages');

            // Update conversation with loaded messages
            dispatch({
              type: actionTypes.SET_CONVERSATIONS,
              payload: state.conversations.map(conv =>
                conv.id === id
                  ? { ...conv, messages }
                  : conv
              )
            });
          }
        } catch (error) {
          console.error('❌ Error loading chat messages:', error);
          dispatch({ type: actionTypes.SET_ERROR, payload: 'Không thể tải tin nhắn: ' + error.message });
        } finally {
          dispatch({ type: actionTypes.SET_LOADING, payload: false });
        }
      } else if (conversation?.messages.length > 0) {
        console.log('✅ Messages already loaded for this conversation');
      } else {
        console.log('ℹ️ No backend chat ID found or conversation not found');
      }
    },

    addMessage: (conversationId, message) => {
      dispatch({ 
        type: actionTypes.ADD_MESSAGE, 
        payload: { conversationId, message } 
      });
    },

    setLoading: (loading) => {
      dispatch({ type: actionTypes.SET_LOADING, payload: loading });
    },

    setError: (error) => {
      dispatch({ type: actionTypes.SET_ERROR, payload: error });
    },

    clearConversations: () => {
      console.log('🧹 Clearing all conversations');
      dispatch({ type: actionTypes.CLEAR_CONVERSATIONS });
    },

    updateConversation: async (id, updates) => {
      console.log('🔄 Updating conversation:', id, updates);
      
      try {
        const conversation = state.conversations.find(conv => conv.id === id);
        if (!conversation) {
          console.log('❌ Conversation not found:', id);
          return;
        }

        // If has backend chat ID, update on backend
        if (conversation.backendChatId) {
          console.log('🌐 Updating chat on backend:', conversation.backendChatId);
          
          const response = await chatAPI.updateChat(conversation.backendChatId, updates);
          console.log('✅ Backend chat updated successfully:', response);
          
          // Update local state with backend response
          dispatch({
            type: actionTypes.UPDATE_CONVERSATION,
            payload: {
              id: id,
              updates: {
                title: response.data.chat.title,
                status: response.data.chat.status,
                metadata: response.data.chat.metadata,
                updatedAt: response.data.chat.updatedAt
              }
            }
          });
        } else {
          console.log('📝 Updating local conversation only');
          // Update local state only
          dispatch({
            type: actionTypes.UPDATE_CONVERSATION,
            payload: { id: id, updates }
          });
        }
        
        console.log('✅ Conversation updated successfully');
      } catch (error) {
        console.error('❌ Error updating conversation:', error);
        dispatch({ type: actionTypes.SET_ERROR, payload: 'Không thể cập nhật cuộc trò chuyện: ' + error.message });
      }
    },

    deleteConversation: async (id) => {
      console.log('🗑️ Deleting conversation:', id);
      
      try {
        const conversation = state.conversations.find(conv => conv.id === id);
        if (!conversation) {
          console.log('❌ Conversation not found:', id);
          return;
        }

        // If has backend chat ID, delete on backend
        if (conversation.backendChatId) {
          console.log('🌐 Deleting chat on backend:', conversation.backendChatId);
          
          const response = await chatAPI.deleteChat(conversation.backendChatId);
          console.log('✅ Backend chat deleted successfully:', response);
        } else {
          console.log('📝 Deleting local conversation only');
        }
        
        // Delete from local state
        dispatch({ type: actionTypes.DELETE_CONVERSATION, payload: id });
        
        console.log('✅ Conversation deleted successfully');
      } catch (error) {
        console.error('❌ Error deleting conversation:', error);
        dispatch({ type: actionTypes.SET_ERROR, payload: 'Không thể xóa cuộc trò chuyện: ' + error.message });
      }
    },

    sendMessage: async (content, files = []) => {
      console.log('ChatContext: sendMessage called with:', { content, files: files?.length });

      // Check if we need to create a new conversation
      let conversationId = state.currentConversationId;
      let currentConv = conversationId ? state.conversations.find(c => c.id === conversationId) : null;

      // If no current conversation, create one automatically
      if (!conversationId || !currentConv) {
        console.log('🆕 No current conversation found, creating new one automatically...');

        // Create a new conversation first
        const newConversationId = uuidv4();
        const newTitle = content.length > 50 ? content.substring(0, 50) + '...' : content;

        dispatch({
          type: actionTypes.ADD_CONVERSATION,
          payload: {
            title: newTitle,
            id: newConversationId,
            backendChatId: null, // Will be set when backend creates the chat
            type: 'general',
            metadata: {}
          }
        });

        // Update our local variables
        conversationId = newConversationId;
        currentConv = {
          id: newConversationId,
          title: newTitle,
          messages: [],
          backendChatId: null,
          type: 'general',
          metadata: {}
        };

        console.log('✅ New conversation created locally:', conversationId);
      }

      const backendChatId = currentConv.backendChatId; // Use existing backendChatId if available
      console.log('📋 Using conversation:', { conversationId, backendChatId });

      // Create user message with file attachments
      const userMessage = {
        id: uuidv4(),
        content: content || (files.length > 0 ? `[Đã gửi ${files.length} file(s)]` : ''),
        role: 'user',
        timestamp: new Date().toISOString(),
        files: files?.map(f => ({
          id: f.id,
          name: f.name,
          type: f.type,
          size: f.size,
          url: f.url,
          downloadUrl: f.downloadUrl,
          mimeType: f.mimeType
        })) || [],
        hasFiles: files && files.length > 0,
        type: files && files.length > 0 ? 'file' : 'text'
      };
      dispatch({
        type: actionTypes.ADD_MESSAGE,
        payload: { conversationId, message: userMessage }
      });

      // We'll create the bot message when we receive the first chunk
      let tempBotMessageId = null;

      dispatch({ type: actionTypes.SET_LOADING, payload: true });
      dispatch({ type: actionTypes.SET_ERROR, payload: null });

      try {
        console.log('ChatContext: Starting stream with backendChatId:', backendChatId, 'message:', content, 'files:', files?.length);

        // If we have files, use the messages-with-files endpoint
        if (files && files.length > 0) {
          console.log('📁 Sending message with files via API...');

          // Create FormData for file upload
          const formData = new FormData();
          formData.append('message', content || '');

          // Add files to FormData
          files.forEach((file, index) => {
            if (file.file) {
              // If file object has the actual File
              formData.append('files', file.file);
            } else if (file.serverId) {
              // If file is already uploaded, just send the ID
              formData.append('fileIds', file.serverId);
            }
          });

          // Use regular API call for file upload (not streaming for now)
          const response = await chatAPI.sendMessageWithFiles(backendChatId, formData);

          if (response.data.success) {
            // Create bot message from response
            const botMessage = {
              id: uuidv4(),
              content: response.data.data.response,
              role: 'assistant',
              timestamp: new Date().toISOString(),
              type: 'text',
              backendMessageId: response.data.data.botMessage._id
            };

            dispatch({
              type: actionTypes.ADD_MESSAGE,
              payload: { conversationId, message: botMessage }
            });

            // Update conversation with backend chat ID if it was created
            if (response.data.data.chatId && !backendChatId) {
              dispatch({
                type: actionTypes.UPDATE_CONVERSATION,
                payload: {
                  id: conversationId,
                  updates: { backendChatId: response.data.data.chatId }
                }
              });
            }
          }
        } else {
          // Regular text message - use streaming
          await chatAPI.streamMessage(
            {
              message: content,
              chatId: backendChatId, // This can be null if it's a new chat, backend should handle
            },
          // onChunk
          (chunk, metadata) => {
            console.log('🔥 ChatContext: Received chunk:', { 
              chunk: chunk?.substring(0, 100), 
              chunkLength: chunk?.length,
              metadata,
              tempBotMessageId: tempBotMessageId 
            });
            
            // Clean chunk by removing typing indicators that might come from API
            const cleanChunk = chunk.replace(/▋+/g, '');
            console.log('🧹 Cleaned chunk:', cleanChunk?.substring(0, 100));
            
            // Create bot message on first chunk if not already created
            if (!tempBotMessageId) {
              tempBotMessageId = uuidv4();
              console.log('🆕 Creating new bot message with ID:', tempBotMessageId);
              const tempBotMessage = {
                id: tempBotMessageId,
                content: cleanChunk, // Start with first chunk
                role: 'assistant',
                timestamp: new Date().toISOString(),
                isStreaming: true,
                type: 'markdown',
                metadata: { ...metadata }
              };
              console.log('💾 Dispatching ADD_MESSAGE with:', tempBotMessage);
              dispatch({
                type: actionTypes.ADD_MESSAGE,
                payload: { conversationId, message: tempBotMessage }
              });
            } else {
              console.log('🔄 Updating existing bot message:', tempBotMessageId);
              // Update existing bot message with new chunk
              dispatch({
                type: actionTypes.UPDATE_MESSAGE,
                payload: {
                  conversationId,
                  messageId: tempBotMessageId,
                  updates: (prevMessage) => {
                    const newContent = (prevMessage?.content || '') + cleanChunk;
                    console.log('📝 Updating message content:', { 
                      prevLength: prevMessage?.content?.length || 0,
                      chunkLength: cleanChunk.length,
                      newLength: newContent.length,
                      newContent: newContent.substring(0, 100) + '...'
                    });
                    return {
                      ...prevMessage,
                      content: newContent,
                      isStreaming: true,
                      metadata: { 
                        ...(prevMessage?.metadata || {}), 
                        ...metadata
                      }
                    };
                  }
                }
              });
            }
          },
          // onComplete
          (data) => {
            console.log('🏁 ChatContext: Stream complete:', data);
            console.log('🏁 Current tempBotMessageId:', tempBotMessageId);
            dispatch({ type: actionTypes.SET_LOADING, payload: false });
            
            // Only update if we have a bot message
            if (tempBotMessageId) {
              console.log('🔚 Finishing stream for message:', tempBotMessageId);
              // Add a small delay to ensure StreamingText has time to finish its animation
              setTimeout(() => {
                dispatch({
                  type: actionTypes.UPDATE_MESSAGE,
                  payload: {
                    conversationId,
                    messageId: tempBotMessageId,
                    updates: (prevMessage) => {
                      const finalContent = (prevMessage?.content || '').replace(/▋+\s*$/, '').trim();
                      console.log('✅ Final message content:', finalContent.substring(0, 100) + '...');
                      return {
                        ...prevMessage,
                        isStreaming: false,
                        // Clean up content by removing any trailing typing indicators
                        content: finalContent,
                        backendMessageId: data.messageId, // If backend sends final message ID
                        metadata: {
                          ...(prevMessage?.metadata || {}),
                          ...(data.metadata || {}), // Add any final metadata
                          completedAt: new Date().toISOString()
                        }
                      };
                    }
                  }
                });
              }, 100); // Small delay to ensure smooth transition
            }
            // If a new chat was created on the backend during this stream, update conversation
            if (data.chatId && !backendChatId) {
              console.log('📝 New chat created by stream, updating conversation with backendChatId:', data.chatId);
              dispatch({
                type: actionTypes.UPDATE_CONVERSATION,
                payload: {
                  id: conversationId,
                  updates: { backendChatId: data.chatId, title: data.chatTitle || currentConv.title }
                }
              });
            }
          },
          // onError
          (error) => {
            console.error('💥 ChatContext: Streaming error:', error);
            dispatch({ type: actionTypes.SET_LOADING, payload: false });
            dispatch({ type: actionTypes.SET_ERROR, payload: error.toString() });
            
            // Only update if we have a bot message
            if (tempBotMessageId) {
              console.log('🚨 Updating bot message with error:', tempBotMessageId);
              dispatch({
                type: actionTypes.UPDATE_MESSAGE,
                payload: {
                  conversationId,
                  messageId: tempBotMessageId,
                  updates: (prevMessage) => ({
                    ...prevMessage,
                    content: (prevMessage?.content || '').replace(/▋+\s*$/, '').trim() + `\n\n[Lỗi: ${error.toString()}]`,
                    isStreaming: false,
                    hasError: true
                  })
                }
              });
            }
          }
        );
        }
      } catch (error) {
        console.error('ChatContext: Error sending message via streamMessage:', error);
        dispatch({ type: actionTypes.SET_LOADING, payload: false });
        dispatch({ type: actionTypes.SET_ERROR, payload: error.message || 'Lỗi không xác định khi gửi tin nhắn' });
        
        // Only update if we have a bot message
        if (tempBotMessageId) {
          // Update the temporary bot message to show error
          dispatch({
            type: actionTypes.UPDATE_MESSAGE,
            payload: {
              conversationId,
              messageId: tempBotMessageId,
              updates: (prevMessage) => ({
                ...prevMessage,
                // Clean up any typing indicators before showing error
                content: (prevMessage?.content || '').replace(/▋+\s*$/, '').trim() + `\n\n[Lỗi: ${error.message || 'Không thể gửi tin nhắn'}]`,
                isStreaming: false,
                hasError: true,
                role: 'error' // Custom role for error messages
              })
            }
          });
        }
      }
    },

    // Action to update a message (e.g., for streaming)
    updateMessage: (conversationId, messageId, updates) => {
      dispatch({
        type: actionTypes.UPDATE_MESSAGE,
        payload: { conversationId, messageId, updates }
      });
    },

    // Load messages for a specific chat
    loadChatMessages: async (conversationId) => {
      const conversation = state.conversations.find(conv => conv.id === conversationId);
      if (!conversation || !conversation.backendChatId) {
        return; // No backend chat ID, skip loading
      }

      try {
        const response = await chatAPI.getChat(conversation.backendChatId);
        if (response?.data?.messages) {
          const messages = response.data.messages.map(msg => ({
            id: uuidv4(),
            content: msg.content,
            role: msg.sender === 'user' ? 'user' : 'assistant',
            timestamp: msg.timestamp,
            type: msg.type || 'text'
          }));

          // Update conversation with loaded messages
          dispatch({
            type: actionTypes.SET_CONVERSATIONS,
            payload: state.conversations.map(conv =>
              conv.id === conversationId
                ? { ...conv, messages }
                : conv
            )
          });
        }
      } catch (error) {
        console.error('Error loading chat messages:', error);
      }
    },

    // Test streaming function for debugging (without real server)
    testStreaming: async (testMessage = "Đây là test streaming. Mỗi từ sẽ xuất hiện từ từ để kiểm tra animation.") => {
      console.log('🧪 Starting test streaming');
      const conversationId = state.currentConversationId;
      if (!conversationId) {
        console.error('No current conversation for test');
        return;
      }

      // Create user message
      const userMessage = {
        id: uuidv4(),
        content: "Test streaming message",
        role: 'user',
        timestamp: new Date().toISOString(),
        type: 'text'
      };
      dispatch({
        type: actionTypes.ADD_MESSAGE,
        payload: { conversationId, message: userMessage }
      });

      // Test streaming simulation
      let tempBotMessageId = null;
      dispatch({ type: actionTypes.SET_LOADING, payload: true });

      // Split test message into chunks
      const words = testMessage.split(' ');
      let currentText = '';

      for (let i = 0; i < words.length; i++) {
        const chunk = (i === 0 ? '' : ' ') + words[i];
        currentText += chunk;
        
        console.log(`🧪 Test chunk ${i + 1}:`, chunk);

        // Simulate the same onChunk logic
        const cleanChunk = chunk.replace(/▋+/g, '');
        
        if (!tempBotMessageId) {
          tempBotMessageId = uuidv4();
          console.log('🧪 Creating test bot message:', tempBotMessageId);
          const tempBotMessage = {
            id: tempBotMessageId,
            content: cleanChunk,
            role: 'assistant',
            timestamp: new Date().toISOString(),
            isStreaming: true,
            type: 'markdown',
            metadata: { chunkNumber: i + 1 }
          };
          dispatch({
            type: actionTypes.ADD_MESSAGE,
            payload: { conversationId, message: tempBotMessage }
          });
        } else {
          dispatch({
            type: actionTypes.UPDATE_MESSAGE,
            payload: {
              conversationId,
              messageId: tempBotMessageId,
              updates: (prevMessage) => {
                const newContent = (prevMessage?.content || '') + cleanChunk;
                console.log('🧪 Test updating content:', newContent);
                return {
                  ...prevMessage,
                  content: newContent,
                  isStreaming: true,
                  metadata: { 
                    ...(prevMessage?.metadata || {}), 
                    chunkNumber: i + 1
                  }
                };
              }
            }
          });
        }

        // Wait between chunks to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Complete the test stream
      console.log('🧪 Test stream complete');
      dispatch({ type: actionTypes.SET_LOADING, payload: false });
      
      if (tempBotMessageId) {
        setTimeout(() => {
          dispatch({
            type: actionTypes.UPDATE_MESSAGE,
            payload: {
              conversationId,
              messageId: tempBotMessageId,
              updates: (prevMessage) => ({
                ...prevMessage,
                isStreaming: false,
                content: (prevMessage?.content || '').replace(/▋+\s*$/, '').trim(),
                metadata: {
                  ...(prevMessage?.metadata || {}),
                  completedAt: new Date().toISOString(),
                  testStream: true
                }
              })
            }
          });
        }, 100);
      }
    },
  };

  // Function to check if message contains chart-related keywords
  const containsChartKeywords = (message) => {
    const keywords = [
      'chart', 'biểu đồ', 'graph', 'đồ thị', 'visualization',
      'biểu đồ', 'chart', 'graph', 'đồ thị', 'visualization',
      'doanh thu', 'thống kê', 'số liệu', 'data', 'analytics',
      'báo cáo', 'report', 'trend', 'xu hướng', 'phân tích'
    ];

    const lowerMessage = message.toLowerCase();
    return keywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
  };

  // Chart templates with HTML and Image data (simulating external API responses)
  const chartTemplates = {
    html: [
      {
        type: 'html',
        content: `
          <div style="width: 100%; height: 300px; background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); border-radius: 8px; padding: 20px; color: white; font-family: Arial, sans-serif;">
            <h3 style="margin: 0 0 20px 0; text-align: center;">Biểu đồ Doanh Thu Q1-Q4</h3>
            <div style="display: flex; align-items: end; justify-content: space-around; height: 200px;">
              <div style="background: rgba(255,255,255,0.8); width: 40px; height: 60%; border-radius: 4px; display: flex; align-items: end; justify-content: center; color: #333; font-weight: bold;">Q1</div>
              <div style="background: rgba(255,255,255,0.8); width: 40px; height: 80%; border-radius: 4px; display: flex; align-items: end; justify-content: center; color: #333; font-weight: bold;">Q2</div>
              <div style="background: rgba(255,255,255,0.8); width: 40px; height: 90%; border-radius: 4px; display: flex; align-items: end; justify-content: center; color: #333; font-weight: bold;">Q3</div>
              <div style="background: rgba(255,255,255,0.8); width: 40px; height: 100%; border-radius: 4px; display: flex; align-items: end; justify-content: center; color: #333; font-weight: bold;">Q4</div>
            </div>
          </div>
        `,
        title: 'Biểu đồ doanh thu theo quý'
      },
      {
        type: 'html',
        content: `
          <div style="width: 100%; height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; padding: 20px; color: white; font-family: Arial, sans-serif;">
            <h3 style="margin: 0 0 20px 0; text-align: center;">Phân Tích Người Dùng</h3>
            <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
              <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(#ff6b6b 0deg 120deg, #4ecdc4 120deg 240deg, #45b7d1 240deg 360deg); position: relative;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #333; font-weight: bold;">100%</div>
              </div>
            </div>
            <div style="display: flex; justify-content: space-around; margin-top: 20px; font-size: 12px;">
              <span>🔴 Mobile 40%</span>
              <span>🟢 Desktop 35%</span>
              <span>🔵 Tablet 25%</span>
            </div>
          </div>
        `,
        title: 'Biểu đồ phân bố thiết bị'
      },
      {
        type: 'html',
        content: `
          <div style="width: 100%; height: 300px; background: linear-gradient(to right, #74b9ff, #0984e3); border-radius: 8px; padding: 20px; color: white; font-family: Arial, sans-serif;">
            <h3 style="margin: 0 0 20px 0; text-align: center;">Xu Hướng Tăng Trưởng</h3>
            <svg width="100%" height="200" style="overflow: visible;">
              <polyline points="20,180 80,160 140,120 200,80 260,40 320,20"
                        fill="none"
                        stroke="white"
                        stroke-width="3"/>
              <circle cx="20" cy="180" r="4" fill="white"/>
              <circle cx="80" cy="160" r="4" fill="white"/>
              <circle cx="140" cy="120" r="4" fill="white"/>
              <circle cx="200" cy="80" r="4" fill="white"/>
              <circle cx="260" cy="40" r="4" fill="white"/>
              <circle cx="320" cy="20" r="4" fill="white"/>
            </svg>
            <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px;">
              <span>Jan</span><span>Feb</span><span>Mar</span><span>Apr</span><span>May</span><span>Jun</span>
            </div>
          </div>
        `,
        title: 'Biểu đồ xu hướng tăng trưởng'
      }
    ],
    image: [
      {
        type: 'image',
        url: 'https://via.placeholder.com/600x300/667eea/ffffff?text=Sample+Chart+1',
        alt: 'Biểu đồ mẫu 1',
        title: 'Biểu đồ thống kê từ API'
      },
      {
        type: 'image',
        url: 'https://via.placeholder.com/600x300/764ba2/ffffff?text=Revenue+Analysis',
        alt: 'Phân tích doanh thu',
        title: 'Biểu đồ phân tích doanh thu'
      },
      {
        type: 'image',
        url: 'https://via.placeholder.com/600x300/4ecdc4/ffffff?text=User+Growth',
        alt: 'Tăng trưởng người dùng',
        title: 'Biểu đồ tăng trưởng người dùng'
      }
    ]
  };

  // Generate chart data based on user message (HTML or Image from external API)
  const generateChartData = (userMessage) => {
    // Randomly choose between HTML and Image (simulating different API responses)
    const chartTypes = ['html', 'image'];
    const randomType = chartTypes[Math.floor(Math.random() * chartTypes.length)];

    const templates = chartTemplates[randomType];
    const randomIndex = Math.floor(Math.random() * templates.length);

    return templates[randomIndex];
  };

  // Get current conversation
  const getCurrentConversation = () => {
    return state.conversations.find(conv => conv.id === state.currentConversationId);
  };

  const value = {
    ...state,
    ...actions,
    getCurrentConversation,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

export default ChatContext;
