// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';

// API utility functions
export const api = {
  // Base request function
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };    // Add auth token if available
    const token = localStorage.getItem('authToken'); // Changed from 'token' to 'authToken'
    console.log('🔑 Auth token found in localStorage:', token ? 'Yes' : 'No');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 Authorization header set');
    }

    try {
      const response = await fetch(url, config);
      
      // Handle non-JSON responses
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Response is not JSON');
      }      const data = await response.json();

      if (!response.ok) {
        console.error('❌ API Error:', {
          status: response.status,
          statusText: response.statusText,
          data: data,
          url: url
        });
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('❌ API request failed:', {
        error: error.message,
        url: url,
        options: options
      });
      throw error;
    }
  },

  // GET request
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  },

  // POST request
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // PUT request
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  },

  // PATCH request
  async patch(endpoint, data) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  },
};

// Auth API functions
export const authAPI = {
  // Login
  async login(email, password) {
    return api.post('/auth/login', { email, password });
  },

  // Register
  async register(userData) {
    return api.post('/auth/register', userData);
  },

  // Get current user
  async getCurrentUser() {
    return api.get('/auth/me');
  },

  // Update profile
  async updateProfile(userData) {
    return api.put('/auth/profile', userData);
  },  // Change password
  async changePassword(currentPassword, newPassword) {
    
    console.log('🔗 Making POST request to: /auth/change-password');
    
    const result = api.post('/auth/change-password', {
      currentPassword,
      newPassword
    });

    return result;
  },

  // Logout
  async logout() {
    return api.post('/auth/logout');
  },
};

// User API functions
export const userAPI = {
  // Get user profile
  async getProfile() {
    return api.get('/users/profile');
  },

  // Update user profile
  async updateProfile(userData) {
    return api.put('/users/profile', userData);
  },

  // Get user credits
  async getCredits() {
    return api.get('/users/credits');
  },
};

// Chat API functions
export const chatAPI = {
  // Create new chat
  async createChat(chatData) {
    return api.post('/chat/create', chatData);
  },

  // Send message to existing chat
  async sendMessageToChat(chatId, messageData) {
    return api.post(`/chat/${chatId}/messages`, messageData);
  },

  // Send message (auto-create chat if no chatId)
  async sendMessage(messageData) {
    return api.post('/chat', messageData);
  },

  // Send message with mock AI response
  async sendMockMessage(messageData) {
    return api.post('/chat/message', messageData);
  },

  // Stream message
  async streamMessage(messageData, onChunk, onComplete, onError) {
    try {
      console.log('Starting stream with data:', messageData);
      
      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(messageData)
      });

      if (!response.ok) {
        // Try to parse error from body
        let errorBody;
        try {
          errorBody = await response.json();
        } catch (e) {
          // Ignore if not JSON
        }
        const errorMessage = errorBody?.message || `HTTP error! status: ${response.status}`;
        console.error('Stream API error response:', response.status, errorBody);
        throw new Error(errorMessage);
      }

      console.log('Stream started, reading response...');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let chunkCount = 0;
      let lastUpdateTime = Date.now();
      const MIN_UPDATE_DELAY = 16; // ~60fps

      const processChunk = (text, metadata) => {
        const now = Date.now();
        const timeSinceLastUpdate = now - lastUpdateTime;
        
        if (timeSinceLastUpdate < MIN_UPDATE_DELAY) {
          setTimeout(() => {
            onChunk(text, metadata);
            lastUpdateTime = Date.now();
          }, MIN_UPDATE_DELAY - timeSinceLastUpdate);
        } else {
          onChunk(text, metadata);
          lastUpdateTime = now;
        }
      };

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('Stream reading done');
          // Process any remaining data in the buffer
          if (buffer.trim()) {
            try {
              // Attempt to parse the remaining buffer as a single JSON object or multiple
              // This part might need adjustment based on how the server finalizes the stream
              const lines = buffer.split('\n').filter(line => line.trim().startsWith('data: '));
              for (const line of lines) {
                const data = JSON.parse(line.slice(6));
                console.log('Final buffer data processing:', data);
                if (data.done) {
                  onComplete(data);
                  return; // Exit if 'done' is received                } else if (data.chunk) {
                   // Clean chunk from typing indicators that might come from server
                   const cleanChunk = (data.chunk || '').replace(/▋+/g, '');
                   processChunk(cleanChunk, {
                    chunkNumber: data.chunkNumber, // Backend sends chunkNumber directly
                    timestamp: data.timestamp     // Backend sends timestamp directly
                  });
                }
              }
              // If no explicit 'done' message in buffer, call onComplete with what we have
              // or a generic completion signal if appropriate.
              // For now, if onComplete hasn't been called, call it.
              // This might need to be more robust based on server behavior.
              if (typeof onComplete === 'function') {
                 // Check if onComplete has already been called by a 'data.done' event
                 // This is a simplified check; a more robust mechanism might be needed.
                 let completed = false;
                 const wrappedOnComplete = (data) => {
                   if(!completed) {
                     completed = true;
                     onComplete(data);
                   }
                 }
                 // If the last processed data had a done flag, it would have called onComplete.
                 // If not, and the stream is done, we call onComplete.
                 // This logic might need refinement based on actual stream termination.
                 // For now, if the loop finishes and onComplete wasn't called by a data.done,
                 // we assume completion.
                 // A more robust way is to ensure the server sends a final 'done' message.
                 // If the buffer processing above didn't trigger onComplete via data.done,
                 // we call it here. This is a fallback.
                 // The document implies a final data.done event.
              }
            } catch (e) {
              console.error('Error parsing final buffer:', e, buffer);
              // onError('Error processing final stream data'); // Optionally call onError
            }
          }
          // Call onComplete if not already called by a 'done' event
          // This ensures onComplete is called if the stream ends without a specific 'done' message in the last chunk
          // However, the spec implies a `data.done` message.
          // Let's rely on the server sending `data: {"done": true, ...}`
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        console.log('Received raw chunk:', chunk);
        buffer += chunk;

        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

        for (const line of lines) {
          if (line.trim().startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              console.log('Parsed SSE data:', data);
              
              if (data.error) {
                console.error('Stream error from server data:', data.error);
                onError(data.error);
                return; // Stop processing on error
              }
              
              if (data.done) {
                console.log('Stream complete event received');
                onComplete(data);
                return; // Stop processing on completion
              }              if (data.chunk) {
                chunkCount++;
                console.log(`Processing chunk ${chunkCount}:`, data.chunk);
                // Clean chunk from typing indicators that might come from server
                const cleanChunk = (data.chunk || '').replace(/▋+/g, '');
                processChunk(cleanChunk, {
                  // Fix: Use chunkNumber and timestamp directly from data as per backend format
                  chunkNumber: data.chunkNumber || chunkCount, // Backend sends chunkNumber directly
                  timestamp: data.timestamp || Date.now() // Backend sends timestamp directly
                });
              }
            } catch (e) {
              console.error('Error parsing SSE data line:', e, line);
              // Optionally, call onError for parsing errors if they should halt the stream
              // onError('Error parsing stream data'); 
              // return; // Decide if parsing error is fatal
            }
          } else if (line.trim()) {
            console.log('Non-data line received:', line); // Log other lines if necessary
          }
        }
      }
    } catch (error) {
      console.error('Stream message function error:', error);
      onError(error.message || 'Unknown streaming error');
    }
  },

  // Get chat details
  async getChat(chatId) {
    return api.get(`/chat/${chatId}`);
  },

  // Get chat history
  async getChatHistory(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/chat/history${queryString ? `?${queryString}` : ''}`);
  },

  // Update chat
  async updateChat(chatId, updateData) {
    return api.put(`/chat/${chatId}`, updateData);
  },

  // Delete chat
  async deleteChat(chatId) {
    return api.delete(`/chat/${chatId}`);
  },

  // Send message with files
  async sendMessageWithFiles(chatId, formData) {
    const token = localStorage.getItem('authToken');

    const url = chatId
      ? `${API_BASE_URL}/chat/${chatId}/messages-with-files`
      : `${API_BASE_URL}/chat/messages-with-files`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
        // Don't set Content-Type for FormData, let browser set it with boundary
      },
      body: formData
    });

    if (!response.ok) {
      let errorBody;
      try {
        errorBody = await response.json();
      } catch (e) {
        // Ignore if not JSON
      }
      const errorMessage = errorBody?.message || `HTTP error! status: ${response.status}`;
      throw new Error(errorMessage);
    }

    return response.json();
  },
};

// Payment API functions
export const paymentAPI = {
  // Create payment
  async createPayment(paymentData) {
    return api.post('/payment/create', paymentData);
  },

  // Get payment history
  async getPaymentHistory() {
    return api.get('/payment/history');
  },

  // Verify payment
  async verifyPayment(paymentId) {
    return api.post(`/payment/verify/${paymentId}`);
  },
};

// File upload API functions
export const fileAPI = {
  // Upload file
  async uploadFile(file, onProgress) {
    const formData = new FormData();
    formData.append('file', file);

    const token = localStorage.getItem('authToken'); // Changed from 'token' to 'authToken'
    
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = (event.loaded / event.total) * 100;
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid JSON response'));
          }
        } else {
          reject(new Error(`Upload failed: ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.open('POST', `${API_BASE_URL}/upload`);
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.send(formData);
    });
  },

  // Get uploaded files
  async getFiles() {
    return api.get('/upload/files');
  },

  // Delete file
  async deleteFile(fileId) {
    return api.delete(`/upload/files/${fileId}`);
  },
};

export default api;
