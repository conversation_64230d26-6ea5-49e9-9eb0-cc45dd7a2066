<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend File Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Frontend File Upload Fix</h1>
    
    <div class="test-section">
        <h2>1. Login Test</h2>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. File Upload Test</h2>
        <input type="file" id="fileInput" multiple accept=".png,.html,.jpg,.pdf,.txt">
        <br>
        <button onclick="testFileUpload()">Test Upload Selected Files</button>
        <div id="uploadResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="logs" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5001/api';
        let authToken = '';
        
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logs.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            
            try {
                log('🔐 Testing login...');
                
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    authToken = data.data.token;
                    resultDiv.innerHTML = '<span class="success">✅ Login successful!</span>';
                    log('✅ Login successful, token obtained', 'success');
                } else {
                    throw new Error(data.message || 'Login failed');
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ Login failed: ${error.message}</span>`;
                log(`❌ Login failed: ${error.message}`, 'error');
            }
        }
        
        async function testFileUpload() {
            const resultDiv = document.getElementById('uploadResult');
            const fileInput = document.getElementById('fileInput');
            
            if (!authToken) {
                resultDiv.innerHTML = '<span class="error">❌ Please login first!</span>';
                log('❌ No auth token, please login first', 'error');
                return;
            }
            
            if (!fileInput.files.length) {
                resultDiv.innerHTML = '<span class="error">❌ Please select files first!</span>';
                log('❌ No files selected', 'error');
                return;
            }
            
            try {
                log(`📁 Testing upload of ${fileInput.files.length} file(s)...`);
                
                for (let i = 0; i < fileInput.files.length; i++) {
                    const file = fileInput.files[i];
                    log(`📤 Uploading: ${file.name} (${file.type}, ${file.size} bytes)`);
                    
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('tags', `test,${file.type.split('/')[0]}`);
                    formData.append('isPublic', 'false');
                    formData.append('metadata', JSON.stringify({
                        source: 'frontend-test',
                        category: file.type.split('/')[0]
                    }));
                    
                    const response = await fetch(`${API_BASE_URL}/files/upload`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                            // Don't set Content-Type for FormData
                        },
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        log(`✅ Upload successful: ${file.name} -> ID: ${data.data.file._id}`, 'success');
                    } else {
                        throw new Error(`Upload failed for ${file.name}: ${data.message}`);
                    }
                }
                
                resultDiv.innerHTML = `<span class="success">✅ All ${fileInput.files.length} file(s) uploaded successfully!</span>`;
                log(`🎉 All uploads completed successfully!`, 'success');
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ Upload failed: ${error.message}</span>`;
                log(`❌ Upload failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-login on page load for convenience
        window.addEventListener('load', () => {
            log('🚀 Frontend test page loaded');
            log('📋 Instructions:');
            log('1. Click "Test Login" to authenticate');
            log('2. Select PNG, HTML, or other supported files');
            log('3. Click "Test Upload Selected Files"');
            log('4. Check the results and logs');
        });
    </script>
</body>
</html>
