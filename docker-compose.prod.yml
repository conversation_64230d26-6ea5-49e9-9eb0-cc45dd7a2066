version: '3.8'

services:
  # Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        - VITE_API_URL=http://imta.ai/api
    container_name: imta-app-prod
    restart: unless-stopped
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - PORT=5001
      - VITE_API_URL=http://imta.ai/api
      - MONGODB_URI=********************************************/imta-ai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=imta123456
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=imta123456
      - MINIO_USE_SSL=false
      - MINIO_BUCKET=imta-ai
      - CORS_ORIGIN=*
      - JWT_SECRET=your-super-secret-jwt-key-here
      # Langflow API Configuration - UPDATE WITH VALID PRODUCTION KEYS
      - LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/7bf86c7f-4b32-4870-b8e6-edf4e8f0e420
      - LANGFLOW_API_KEY=sk-oe5lrC2ONAOGWNNLpxENbcLd2ouG24Ck6xJKhpsHNsQ
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - imta-network
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./public:/app/public:ro
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB
  mongodb:
    image: mongo:7.0
    container_name: imta-mongodb-prod
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: imta_user
      MONGO_INITDB_ROOT_PASSWORD: imta123456
      MONGO_INITDB_DATABASE: imta-ai
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - imta-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis
  redis:
    image: redis:7.2-alpine
    container_name: imta-redis-prod
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --requirepass imta123456
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - imta-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO
  minio:
    image: minio/minio:latest
    container_name: imta-minio-prod
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: imta123456
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - imta-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Mongo Express (Optional - for admin)
  mongo-express:
    image: mongo-express:latest
    container_name: imta-mongo-express-prod
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: imta_user
      ME_CONFIG_MONGODB_ADMINPASSWORD: imta123456
      ME_CONFIG_MONGODB_URL: ********************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - imta-network

  # Redis Commander (Optional - for admin)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: imta-redis-commander-prod
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:imta123456
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - imta-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  imta-network:
    driver: bridge 