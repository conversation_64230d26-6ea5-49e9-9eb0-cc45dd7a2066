# K<PERSON> Hoạch <PERSON>ển <PERSON> Upload File với MinIO và Node.js

## 1. <PERSON><PERSON> Tích Cấu Trúc Hiện Tạ<PERSON>

### 1.1 Cấu Trúc Server Hiện Tại
- **Framework**: Express.js với Node.js 20
- **Database**: MongoDB với Mongoose ODM
- **Authentication**: JWT-based authentication
- **Architecture**: RESTful API với middleware pattern
- **Containerization**: Docker với multi-stage build

### 1.2 Cấu Hình MinIO Hiện Có
```yaml
# docker-compose.yml
minio:
  image: minio/minio:latest
  ports: ["9000:9000", "9001:9001"]
  environment:
    MINIO_ROOT_USER: minioadmin
    MINIO_ROOT_PASSWORD: imta123456
```

```env
# server/env.example
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=imta123456
MINIO_USE_SSL=false
MINIO_BUCKET=imta-ai
```

### 1.3 Cấu Trúc Routes Hiện Tại
- `/api/auth` - Authentication routes
- `/api/users` - User management
- `/api/chat` - Chat functionality
- `/api/payment` - Payment processing

## 2. Yêu cầu và Dependencies Cần Thêm

### 2.1 NPM Packages Cần Cài Đặt
```json
{
  "dependencies": {
    "minio": "^7.1.3",
    "multer": "^1.4.5-lts.1",
    "mime-types": "^2.1.35",
    "uuid": "^9.0.1",
    "sharp": "^0.33.0"
  }
}
```

### 2.2 Chức Năng Cần Triển Khai
1. **Upload File API** - Upload files to MinIO
2. **Download File API** - Retrieve files from MinIO
3. **File Management API** - List, delete, get file info
4. **File Model** - MongoDB schema for file metadata
5. **File Middleware** - Validation, size limits, type checking
6. **MinIO Client Configuration** - Connection and bucket management

## 3. Kiến Trúc Giải Pháp

### 3.1 Luồng Upload File
```
Client → Express Middleware → Multer → MinIO → MongoDB (metadata) → Response
```

### 3.2 Cấu Trúc File Mới Cần Tạo
```
server/
├── config/
│   └── minio.js              # MinIO client configuration
├── middleware/
│   └── fileUpload.js         # File upload middleware
├── models/
│   └── File.js               # File metadata model
├── routes/
│   └── files.js              # File management routes
├── services/
│   └── fileService.js        # File business logic
└── utils/
    └── fileUtils.js          # File utility functions
```

## 4. Implementation Chi Tiết

### 4.1 MinIO Client Configuration (`server/config/minio.js`)
```javascript
const Minio = require('minio');

const minioClient = new Minio.Client({
  endPoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT) || 9000,
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'imta123456'
});

const BUCKET_NAME = process.env.MINIO_BUCKET || 'imta-ai';

// Initialize bucket
const initializeBucket = async () => {
  const exists = await minioClient.bucketExists(BUCKET_NAME);
  if (!exists) {
    await minioClient.makeBucket(BUCKET_NAME, 'us-east-1');
    console.log(`✅ MinIO bucket '${BUCKET_NAME}' created`);
  }
};

module.exports = { minioClient, BUCKET_NAME, initializeBucket };
```

### 4.2 File Model (`server/models/File.js`)
```javascript
const mongoose = require('mongoose');

const fileSchema = new mongoose.Schema({
  filename: { type: String, required: true },
  originalName: { type: String, required: true },
  mimeType: { type: String, required: true },
  size: { type: Number, required: true },
  path: { type: String, required: true }, // MinIO object path
  uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  isPublic: { type: Boolean, default: false },
  tags: [String],
  metadata: { type: Map, of: String },
  downloadCount: { type: Number, default: 0 }
}, {
  timestamps: true
});

fileSchema.index({ uploadedBy: 1, createdAt: -1 });
fileSchema.index({ filename: 1 });
fileSchema.index({ mimeType: 1 });

module.exports = mongoose.model('File', fileSchema);
```

### 4.3 File Upload Middleware (`server/middleware/fileUpload.js`)
```javascript
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'text/plain', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('File type not allowed'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  }
});

module.exports = { upload };
```

### 4.4 File Service (`server/services/fileService.js`)
```javascript
const { minioClient, BUCKET_NAME } = require('../config/minio');
const File = require('../models/File');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

class FileService {
  static async uploadFile(fileBuffer, originalName, mimeType, userId) {
    const fileExtension = path.extname(originalName);
    const filename = `${uuidv4()}${fileExtension}`;
    const objectPath = `uploads/${userId}/${filename}`;

    // Upload to MinIO
    await minioClient.putObject(BUCKET_NAME, objectPath, fileBuffer, {
      'Content-Type': mimeType,
      'X-Uploaded-By': userId
    });

    // Save metadata to MongoDB
    const fileDoc = new File({
      filename,
      originalName,
      mimeType,
      size: fileBuffer.length,
      path: objectPath,
      uploadedBy: userId
    });

    await fileDoc.save();
    return fileDoc;
  }

  static async getFileStream(fileId, userId) {
    const file = await File.findOne({ _id: fileId, uploadedBy: userId });
    if (!file) throw new Error('File not found');

    const stream = await minioClient.getObject(BUCKET_NAME, file.path);
    
    // Increment download count
    await File.findByIdAndUpdate(fileId, { $inc: { downloadCount: 1 } });
    
    return { stream, file };
  }

  static async deleteFile(fileId, userId) {
    const file = await File.findOne({ _id: fileId, uploadedBy: userId });
    if (!file) throw new Error('File not found');

    // Delete from MinIO
    await minioClient.removeObject(BUCKET_NAME, file.path);
    
    // Delete from MongoDB
    await File.findByIdAndDelete(fileId);
    
    return file;
  }

  static async getUserFiles(userId, page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    const files = await File.find({ uploadedBy: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-path'); // Don't expose MinIO path

    const total = await File.countDocuments({ uploadedBy: userId });
    
    return { files, total, page, totalPages: Math.ceil(total / limit) };
  }
}

module.exports = FileService;
```

## 5. API Routes Implementation

### 5.1 File Routes (`server/routes/files.js`)
```javascript
const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { upload } = require('../middleware/fileUpload');
const FileService = require('../services/fileService');
const { sendLocalizedSuccess, sendLocalizedError } = require('../utils/i18n');

// POST /api/files/upload - Upload file
router.post('/upload', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return sendLocalizedError(res, 400, 'files.upload.no_file');
    }

    const file = await FileService.uploadFile(
      req.file.buffer,
      req.file.originalname,
      req.file.mimetype,
      req.user._id
    );

    sendLocalizedSuccess(res, 201, 'files.upload.success', { file });
  } catch (error) {
    console.error('File upload error:', error);
    sendLocalizedError(res, 500, 'files.upload.failed');
  }
});

// GET /api/files - Get user files
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const result = await FileService.getUserFiles(req.user._id, parseInt(page), parseInt(limit));
    
    sendLocalizedSuccess(res, 200, 'files.list.success', result);
  } catch (error) {
    console.error('Get files error:', error);
    sendLocalizedError(res, 500, 'files.list.failed');
  }
});

// GET /api/files/:id/download - Download file
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    const { stream, file } = await FileService.getFileStream(req.params.id, req.user._id);
    
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);
    
    stream.pipe(res);
  } catch (error) {
    console.error('File download error:', error);
    sendLocalizedError(res, 404, 'files.download.not_found');
  }
});

// DELETE /api/files/:id - Delete file
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    await FileService.deleteFile(req.params.id, req.user._id);
    sendLocalizedSuccess(res, 200, 'files.delete.success');
  } catch (error) {
    console.error('File delete error:', error);
    sendLocalizedError(res, 404, 'files.delete.not_found');
  }
});

module.exports = router;
```

## 6. Cấu Hình và Deployment

### 6.1 Cập Nhật app.js
```javascript
// Thêm vào server/app.js
const fileRoutes = require('./routes/files');
app.use('/api/files', fileRoutes);

// Initialize MinIO bucket
const { initializeBucket } = require('./config/minio');
initializeBucket().catch(console.error);
```

### 6.2 Cập Nhật package.json
```bash
cd server
npm install minio multer mime-types uuid sharp
```

### 6.3 Cập Nhật models/index.js
```javascript
// Thêm File model
const File = require('./File');

// Thêm vào exports và createIndexes
module.exports = { User, Credit, Chat, Message, Payment, File, connectDB, disconnectDB };
```

## 7. Testing và Security

### 7.1 Security Considerations
- File type validation
- File size limits
- User authentication required
- Path traversal protection
- Virus scanning (optional)

### 7.2 Testing Endpoints
```bash
# Upload file
curl -X POST http://localhost:5001/api/files/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@test.jpg"

# Get files
curl -X GET http://localhost:5001/api/files \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Download file
curl -X GET http://localhost:5001/api/files/FILE_ID/download \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o downloaded_file.jpg
```

## 8. Kết Luận

Giải pháp này cung cấp:
- ✅ Upload file an toàn với validation
- ✅ Lưu trữ scalable với MinIO
- ✅ Metadata management với MongoDB
- ✅ Authentication và authorization
- ✅ RESTful API design
- ✅ Error handling và i18n support
- ✅ Docker-ready deployment

Triển khai theo thứ tự: Dependencies → Models → Config → Services → Routes → Testing
