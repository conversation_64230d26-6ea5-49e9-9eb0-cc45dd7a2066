# Production Environment Configuration
# Copy this file to .env for production deployment

# Application Configuration
NODE_ENV=production
PORT=5001
CORS_ORIGIN=https://imta.ai,https://www.imta.ai

# Database Configuration
MONGODB_URI=****************************************************

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=imta123456

# MinIO Configuration
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=imta123456
MINIO_USE_SSL=false
MINIO_BUCKET=imta-ai

# Langflow API Configuration - PRODUCTION
# ⚠️ IMPORTANT: Update these with your valid production API keys
LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/7bf86c7f-4b32-4870-b8e6-edf4e8f0e420
LANGFLOW_API_KEY=sk-oe5lrC2ONAOGWNNLpxENbcLd2ouG24Ck6xJKhpsHNsQ

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_EXPIRE=7d

# File Upload Configuration
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain,text/csv,text/html,application/json,text/xml,application/xml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/zip,application/x-rar-compressed

# Email Configuration (if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000

# Production Optimizations
ENABLE_COMPRESSION=true
ENABLE_HELMET=true
TRUST_PROXY=true
