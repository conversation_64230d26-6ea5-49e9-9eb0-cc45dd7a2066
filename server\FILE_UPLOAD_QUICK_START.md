# File Upload API - Quick Start Guide

## 🚀 Khởi Động Nhanh

### 1. Khởi động server:
```bash
cd server
npm run dev
```

### 2. Ki<PERSON><PERSON> tra health:
```bash
curl http://localhost:5001/api/health
```

### 3. Đ<PERSON><PERSON> k<PERSON> user:
```bash
curl -X POST http://localhost:5001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"123456","fullName":"Test User"}'
```

### 4. Đ<PERSON>ng nhập lấy token:
```bash
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'
```

### 5. Upload file:
```bash
curl -X POST http://localhost:5001/api/files/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.jpg" \
  -F "tags=image,test" \
  -F "isPublic=false"
```

## 📋 API Endpoints

| Method | Endpoint | Mô tả |
|--------|----------|-------|
| POST | `/api/files/upload` | Upload single file |
| POST | `/api/files/upload-multiple` | Upload multiple files |
| GET | `/api/files` | Lấy danh sách files |
| GET | `/api/files/stats` | Thống kê files |
| GET | `/api/files/:id` | Thông tin file |
| GET | `/api/files/:id/download` | Download file |
| GET | `/api/files/:id/view` | Xem file inline |
| PUT | `/api/files/:id` | Cập nhật metadata |
| DELETE | `/api/files/:id` | Xóa file |

## 🔧 Cấu Hình

### Environment Variables:
```env
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=imta123456
MINIO_BUCKET=imta-ai
MAX_FILE_SIZE=52428800  # 50MB
```

### File Types Supported:
- Images: jpg, png, gif, webp, bmp, svg
- Documents: pdf, txt, doc, docx, xls, xlsx, ppt, pptx
- Archives: zip, rar, 7z
- Audio: mp3, wav, ogg, mp4
- Video: mp4, mpeg, mov, avi, webm

## 🧪 Test Script

Chạy demo test:
```bash
node demo-test.js
```

## 📁 File Structure

```
server/
├── config/
│   └── minio.js              # MinIO configuration
├── middleware/
│   └── fileUpload.js         # Upload middleware
├── models/
│   └── File.js               # File model
├── routes/
│   └── files.js              # File routes
├── services/
│   └── fileService.js        # File business logic
└── test-file-api.http        # HTTP test file
```

## 🔒 Security Features

- JWT authentication required
- File type validation
- File size limits
- User isolation
- Path traversal protection
- MIME type checking

## 📊 Response Format

### Success Response:
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "file": {
      "_id": "file_id",
      "originalName": "test.jpg",
      "mimeType": "image/jpeg",
      "size": 12345,
      "tags": ["image", "test"],
      "createdAt": "2025-06-19T...",
      "downloadUrl": "/api/files/file_id/download"
    }
  }
}
```

### Error Response:
```json
{
  "success": false,
  "message": "File type not allowed",
  "code": "INVALID_FILE_TYPE"
}
```

## 🎯 Usage Examples

### JavaScript/Axios:
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('tags', 'image,profile');

const response = await axios.post('/api/files/upload', formData, {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'multipart/form-data'
  }
});
```

### cURL:
```bash
curl -X POST http://localhost:5001/api/files/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.jpg" \
  -F "tags=photo,vacation" \
  -F "isPublic=true"
```

## 🚨 Troubleshooting

### Common Issues:

1. **MinIO Connection Error:**
   - Kiểm tra MinIO service đang chạy
   - Verify credentials trong .env

2. **File Upload Failed:**
   - Kiểm tra file size limit
   - Verify file type allowed
   - Check authentication token

3. **Permission Denied:**
   - Ensure JWT token valid
   - Check user has access to file

### Debug Commands:
```bash
# Check MinIO status
curl http://localhost:9000/minio/health/live

# Check server health
curl http://localhost:5001/api/health

# View server logs
npm run dev
```

## 📞 Support

Nếu gặp vấn đề, kiểm tra:
1. Server logs trong terminal
2. MinIO console tại http://localhost:9001
3. MongoDB connection
4. File permissions

**Chức năng upload file đã sẵn sàng sử dụng!** 🎉
