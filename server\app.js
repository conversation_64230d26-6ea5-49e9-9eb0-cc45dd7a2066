const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const { connectDB } = require('./models');
const { i18nMiddleware, sendLocalizedError } = require('./utils/i18n');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5001;
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

// Connect to MongoDB
connectDB()
  .then(() => {
    console.log('MongoDB connected successfully');

    // Initialize MinIO bucket after MongoDB connection
    const { initializeBucket } = require('./config/minio');
    return initializeBucket();
  })
  .then(() => {
    console.log('MinIO bucket initialized successfully');
  })
  .catch((error) => {
    console.error('Failed to connect to MongoDB or initialize MinIO:', error);
    process.exit(1);
  });

// CORS configuration - Allow all origins in production for testing
const corsOptions = {
  origin: true, // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language', 'X-Requested-With'],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  credentials: true,
  maxAge: 86400,
  preflightContinue: false,
  optionsSuccessStatus: 204
};

// Middleware
app.use(cors(corsOptions));

// Add CORS headers for preflight requests
app.options('*', cors(corsOptions));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// i18n middleware (must be before routes)
app.use(i18nMiddleware);

// API Routes (MUST come before static files and catch-all route)
const authRoutes = require('./routes/auth');
app.use('/api/auth', authRoutes);

const userRoutes = require('./routes/user');
app.use('/api/users', userRoutes);

const paymentRoutes = require('./routes/payment');
app.use('/api/payment', paymentRoutes);

const chatRoutes = require('./routes/chat');
app.use('/api/chat', chatRoutes);

const fileRoutes = require('./routes/files');
app.use('/api/files', fileRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: req.t('common.server.health_check'),
    language: req.language,
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version
  });
});

// Serve static files from the Vite build in production
if (isProduction) {
  const distPath = path.join(__dirname, '../dist');
  const indexPath = path.join(distPath, 'index.html');
  
  console.log('Production mode enabled');
  console.log('Checking for static files in:', distPath);
  
  if (fs.existsSync(distPath)) {
    // Serve static files from the dist directory
    app.use(express.static(distPath, {
      maxAge: '1y',
      etag: true,
      lastModified: true,
      setHeaders: (res, path) => {
        // Set cache control for static assets
        if (path.endsWith('.html')) {
          res.setHeader('Cache-Control', 'no-cache');
        } else {
          res.setHeader('Cache-Control', 'public, max-age=31536000');
        }
      }
    }));
    console.log('✅ Serving static files from:', distPath);
    
    // Serve index.html for all NON-API routes (SPA) - MUST be LAST
    app.get('*', (req, res) => {
      // Don't serve SPA for API routes
      if (req.path.startsWith('/api/')) {
        return res.status(404).json({ error: 'API route not found' });
      }
      
      if (fs.existsSync(indexPath)) {
        console.log('✅ Serving index.html for route:', req.path);
        res.sendFile(indexPath);
      } else {
        console.error('❌ index.html not found at:', indexPath);
        res.status(404).json({ 
          error: 'Frontend not found',
          path: indexPath,
          exists: fs.existsSync(indexPath)
        });
      }
    });
  } else {
    console.error('❌ Dist directory not found:', distPath);
  }
}

// Error handling middleware for API routes
app.use('/api/*', (err, req, res, next) => {
  console.error('API Error:', {
    path: req.path,
    method: req.method,
    error: err.message,
    stack: isDevelopment ? err.stack : undefined
  });
  
  // Handle specific error types
  if (err.name === 'ValidationError') {
    return sendLocalizedError(res, 400, 'common.validation.error');
  }
  if (err.name === 'UnauthorizedError') {
    return sendLocalizedError(res, 401, 'common.auth.unauthorized');
  }
  
  sendLocalizedError(res, 500, 'common.server.internal_error');
});

// 404 handler for development (only for non-API routes)
if (isDevelopment) {
  app.use('*', (req, res) => {
    console.log('404 Not Found:', req.method, req.originalUrl);
    sendLocalizedError(res, 404, 'common.server.route_not_found');
  });
}

// Start server
app.listen(PORT, () => {
  console.log(`
🚀 Server is running!
📝 Environment: ${process.env.NODE_ENV}
🔗 Health check: http://localhost:${PORT}/api/health
${isProduction ? `🌐 Frontend: http://localhost:${PORT}` : `⚡ Vite dev server: http://localhost:5173`}
  `);
});
