const Minio = require('minio');

/**
 * <PERSON><PERSON><PERSON> hình MinIO Client
 * S<PERSON> dụng thông tin từ docker-compose.yml và env.example
 */
const minioClient = new Minio.Client({
  endPoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT) || 9000,
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'imta123456'
});

// Tên bucket mặc định
const BUCKET_NAME = process.env.MINIO_BUCKET || 'imta-ai';

/**
 * Khởi tạo bucket nếu chưa tồn tại
 * @returns {Promise<void>}
 */
const initializeBucket = async () => {
  try {
    console.log('🔍 Checking MinIO bucket existence...');
    
    // <PERSON><PERSON><PERSON> tra bucket có tồn tại không
    const exists = await minioClient.bucketExists(BUCKET_NAME);
    
    if (!exists) {
      console.log(`📦 Creating MinIO bucket '${BUCKET_NAME}'...`);
      await minioClient.makeBucket(BUCKET_NAME, 'us-east-1');
      console.log(`✅ MinIO bucket '${BUCKET_NAME}' created successfully`);
      
      // Thiết lập policy cho bucket (optional - cho phép public read nếu cần)
      const policy = {
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: { AWS: ['*'] },
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${BUCKET_NAME}/public/*`]
          }
        ]
      };
      
      // Uncomment nếu muốn cho phép public access cho thư mục public
      // await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));
      
    } else {
      console.log(`✅ MinIO bucket '${BUCKET_NAME}' already exists`);
    }
  } catch (error) {
    console.error('❌ Error initializing MinIO bucket:', error);
    throw error;
  }
};

/**
 * Kiểm tra kết nối MinIO
 * @returns {Promise<boolean>}
 */
const checkConnection = async () => {
  try {
    await minioClient.listBuckets();
    console.log('✅ MinIO connection successful');
    return true;
  } catch (error) {
    console.error('❌ MinIO connection failed:', error.message);
    return false;
  }
};

/**
 * Tạo presigned URL để upload file trực tiếp từ client (optional)
 * @param {string} objectName - Tên object trong bucket
 * @param {number} expiry - Thời gian hết hạn (giây), mặc định 24h
 * @returns {Promise<string>}
 */
const generatePresignedUploadUrl = async (objectName, expiry = 24 * 60 * 60) => {
  try {
    const url = await minioClient.presignedPutObject(BUCKET_NAME, objectName, expiry);
    return url;
  } catch (error) {
    console.error('Error generating presigned upload URL:', error);
    throw error;
  }
};

/**
 * Tạo presigned URL để download file (optional)
 * @param {string} objectName - Tên object trong bucket
 * @param {number} expiry - Thời gian hết hạn (giây), mặc định 1h
 * @returns {Promise<string>}
 */
const generatePresignedDownloadUrl = async (objectName, expiry = 60 * 60) => {
  try {
    const url = await minioClient.presignedGetObject(BUCKET_NAME, objectName, expiry);
    return url;
  } catch (error) {
    console.error('Error generating presigned download URL:', error);
    throw error;
  }
};

module.exports = {
  minioClient,
  BUCKET_NAME,
  initializeBucket,
  checkConnection,
  generatePresignedUploadUrl,
  generatePresignedDownloadUrl
};
