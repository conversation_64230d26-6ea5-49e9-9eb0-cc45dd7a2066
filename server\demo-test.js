/**
 * Demo script để test File Upload API
 * Chạy: node demo-test.js
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';
let authToken = '';

// Test user data
const testUser = {
  username: 'demouser',
  email: '<EMAIL>',
  password: '123456',
  fullName: 'Demo User'
};

console.log('🚀 Bắt đầu demo File Upload API...\n');

async function testAPI() {
  try {
    // 1. Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log(`✅ Health Check: ${healthResponse.data.message}\n`);

    // 2. Register user (có thể fail nếu user đã tồn tại)
    console.log('2. Testing User Registration...');
    try {
      await axios.post(`${BASE_URL}/api/auth/register`, testUser);
      console.log('✅ User registered successfully\n');
    } catch (error) {
      console.log('⚠️ User registration failed (user may already exist)\n');
    }

    // 3. Login để lấy token
    console.log('3. Testing User Login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    authToken = loginResponse.data.data.token;
    console.log('✅ Login successful, token obtained\n');

    // Headers với token
    const headers = {
      'Authorization': `Bearer ${authToken}`
    };

    // 4. Tạo test file
    console.log('4. Creating test file...');
    const testFilePath = path.join(__dirname, 'demo-test-file.txt');
    const testFileContent = `Demo test file for upload\nCreated at: ${new Date().toISOString()}\nContent: This is a demonstration of file upload functionality.`;
    fs.writeFileSync(testFilePath, testFileContent);
    console.log(`✅ Test file created: ${testFilePath}\n`);

    // 5. Upload file
    console.log('5. Testing File Upload...');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('tags', 'demo,test,upload');
    formData.append('isPublic', 'false');
    formData.append('metadata', JSON.stringify({
      description: 'Demo file upload',
      category: 'testing',
      source: 'demo-script'
    }));

    const uploadResponse = await axios.post(`${BASE_URL}/api/files/upload`, formData, {
      headers: {
        ...headers,
        ...formData.getHeaders()
      }
    });

    const fileId = uploadResponse.data.data.file._id;
    console.log(`✅ File uploaded successfully!`);
    console.log(`   File ID: ${fileId}`);
    console.log(`   Original Name: ${uploadResponse.data.data.file.originalName}`);
    console.log(`   Size: ${uploadResponse.data.data.file.size} bytes\n`);

    // 6. Get files list
    console.log('6. Testing Get Files List...');
    const filesResponse = await axios.get(`${BASE_URL}/api/files`, { headers });
    console.log(`✅ Files list retrieved successfully!`);
    console.log(`   Total files: ${filesResponse.data.data.pagination.total}`);
    console.log(`   Current page: ${filesResponse.data.data.pagination.page}\n`);

    // 7. Get file stats
    console.log('7. Testing Get File Stats...');
    const statsResponse = await axios.get(`${BASE_URL}/api/files/stats`, { headers });
    console.log(`✅ File stats retrieved successfully!`);
    console.log(`   Total files: ${statsResponse.data.data.stats.totalFiles}`);
    console.log(`   Total size: ${statsResponse.data.data.stats.totalSize} bytes\n`);

    // 8. Get file info
    console.log('8. Testing Get File Info...');
    const fileInfoResponse = await axios.get(`${BASE_URL}/api/files/${fileId}`, { headers });
    console.log(`✅ File info retrieved successfully!`);
    console.log(`   File name: ${fileInfoResponse.data.data.file.originalName}`);
    console.log(`   MIME type: ${fileInfoResponse.data.data.file.mimeType}`);
    console.log(`   Tags: ${fileInfoResponse.data.data.file.tags.join(', ')}\n`);

    // 9. Download file
    console.log('9. Testing File Download...');
    const downloadPath = path.join(__dirname, 'demo-downloaded-file.txt');
    const downloadResponse = await axios.get(`${BASE_URL}/api/files/${fileId}/download`, {
      headers,
      responseType: 'stream'
    });
    
    const writer = fs.createWriteStream(downloadPath);
    downloadResponse.data.pipe(writer);
    
    await new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });

    const downloadedContent = fs.readFileSync(downloadPath, 'utf8');
    console.log(`✅ File downloaded successfully!`);
    console.log(`   Downloaded to: ${downloadPath}`);
    console.log(`   Content length: ${downloadedContent.length} characters\n`);

    // 10. Update file metadata
    console.log('10. Testing Update File Metadata...');
    const updateData = {
      tags: ['updated', 'demo', 'test'],
      metadata: {
        description: 'Updated demo file',
        category: 'testing-updated',
        lastModified: new Date().toISOString()
      },
      isPublic: true
    };

    await axios.put(`${BASE_URL}/api/files/${fileId}`, updateData, { headers });
    console.log('✅ File metadata updated successfully!\n');

    // 11. Delete file
    console.log('11. Testing Delete File...');
    await axios.delete(`${BASE_URL}/api/files/${fileId}`, { headers });
    console.log('✅ File deleted successfully!\n');

    // Cleanup
    console.log('🧹 Cleaning up...');
    fs.unlinkSync(testFilePath);
    fs.unlinkSync(downloadPath);
    console.log('✅ Cleanup completed\n');

    console.log('🎉 Demo completed successfully!');
    console.log('📋 All API endpoints tested and working properly:');
    console.log('   ✅ Health Check');
    console.log('   ✅ User Registration');
    console.log('   ✅ User Login');
    console.log('   ✅ File Upload');
    console.log('   ✅ Get Files List');
    console.log('   ✅ Get File Stats');
    console.log('   ✅ Get File Info');
    console.log('   ✅ File Download');
    console.log('   ✅ Update File Metadata');
    console.log('   ✅ Delete File');

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Chạy demo
testAPI();
