{"auth": {"register": {"success": "User registered successfully", "missing_fields": "Username, email, password and fullName are required", "password_length": "Password must be at least 6 characters", "user_exists": "User with this email or username already exists", "validation_failed": "Registration data is invalid", "failed": "Registration failed"}, "login": {"success": "Login successful", "missing_fields": "Email and password are required", "invalid_credentials": "Invalid email or password", "account_inactive": "Account is not active", "failed": "<PERSON><PERSON> failed"}, "logout": {"success": "Logout successful", "failed": "Logout failed"}, "profile": {"get_success": "User information retrieved successfully", "update_success": "Profile updated successfully", "update_failed": "Failed to update profile", "get_failed": "Failed to get user information"}, "password": {"change_success": "Password changed successfully", "missing_fields": "Current password and new password are required", "invalid_current": "Current password is incorrect", "change_failed": "Failed to change password"}, "validation": {"invalid_email": "Invalid email address", "invalid_username": "Userna<PERSON> must be at least 3 characters and no more than 30 characters", "invalid_password": "Password must be at least 6 characters", "invalid_fullname": "Full name cannot exceed 100 characters", "invalid_phone": "Invalid phone number"}}, "payment": {"create": {"success": "Payment created successfully", "invalid_amount": "Amount must be at least 1,000 VND", "invalid_method": "Invalid payment method", "failed": "Failed to create payment", "description": "Deposit to {user}'s account", "sepay_error": "Sepay API error"}, "callback": {"success": "Payment completed successfully", "failed": "Payment failed or cancelled", "missing_params": "Missing required parameters", "invalid_signature": "Invalid signature", "payment_not_found": "Payment not found", "callback_failed": "Payment callback failed", "failed_reason": "Payment failed or cancelled"}, "webhook": {"processing_failed": "Webhook processing failed"}, "history": {"get_failed": "Failed to get payment history"}, "details": {"get_failed": "Failed to get payment details", "not_found": "Payment not found"}}, "chat": {"message": {"required": "Message is required and must be a string", "success": "Message sent successfully", "send_success": "Message sent successfully", "send_failed": "Failed to send message", "process_failed": "Failed to process message"}, "history": {"success": "Chat history retrieved successfully", "get_failed": "Failed to get chat history"}, "details": {"success": "Chat details retrieved successfully", "get_failed": "Failed to get chat details", "not_found": "Cha<PERSON> not found"}, "create": {"success": "<PERSON><PERSON> created successfully", "title_required": "Title is required", "failed": "Failed to create chat"}, "update": {"success": "Chat updated successfully", "not_found": "Cha<PERSON> not found", "failed": "Failed to update chat"}, "delete": {"success": "<PERSON><PERSON> deleted successfully", "not_found": "Cha<PERSON> not found", "failed": "Failed to delete chat"}, "invalid_id": "Invalid chat ID"}, "user": {"list": {"get_failed": "Failed to fetch users"}}, "common": {"auth": {"token_required": "Access token required", "invalid_token": "Invalid token", "token_expired": "Token expired", "user_not_found": "User not found", "account_inactive": "Account is not active", "auth_failed": "Authentication failed", "insufficient_permissions": "Insufficient permissions", "access_denied": "Access denied to this resource", "resource_user_id_required": "Resource user ID required"}, "validation": {"required_field": "This field is required", "invalid_format": "Invalid format", "min_length": "Must be at least {min} characters", "max_length": "Cannot exceed {max} characters", "invalid_email": "Invalid email address", "invalid_phone": "Invalid phone number"}, "server": {"internal_error": "Something went wrong!", "route_not_found": "Route not found", "health_check": "Server is running"}, "pagination": {"invalid_page": "Invalid page number", "invalid_limit": "Invalid limit"}}, "langflow": {"connection_error": "I'm sorry, I'm having trouble connecting to my language model right now. Please try again later.", "api_error": "Langflow API error"}, "files": {"upload": {"success": "File uploaded successfully", "multiple_success": "Files uploaded successfully", "no_file": "No file provided", "no_files": "No files provided", "failed": "File upload failed", "duplicate": "File already exists"}, "list": {"success": "Files retrieved successfully", "failed": "Failed to retrieve files"}, "info": {"success": "File information retrieved successfully", "failed": "Failed to retrieve file information"}, "stats": {"success": "File statistics retrieved successfully", "failed": "Failed to retrieve file statistics"}, "download": {"not_found": "File not found or access denied", "failed": "File download failed", "stream_error": "Error streaming file"}, "view": {"not_found": "File not found or access denied", "failed": "File view failed", "stream_error": "Error streaming file"}, "update": {"success": "File updated successfully", "failed": "Failed to update file"}, "delete": {"success": "File deleted successfully", "not_found": "File not found or access denied", "failed": "Failed to delete file"}, "not_found": "File not found or access denied"}}