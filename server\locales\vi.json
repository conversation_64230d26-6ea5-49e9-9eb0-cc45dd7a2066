{"auth": {"register": {"success": "<PERSON><PERSON><PERSON> ký tài khoản thành công", "missing_fields": "Username, email, password và fullName là bắt buộc", "password_length": "<PERSON><PERSON>t kh<PERSON>u ph<PERSON>i có ít nhất 6 ký tự", "user_exists": "Người dùng với email hoặc username này đã tồn tại", "validation_failed": "<PERSON><PERSON> liệu đăng ký không hợp lệ", "failed": "<PERSON><PERSON><PERSON> ký thất bại"}, "login": {"success": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "missing_fields": "Email và password là b<PERSON> buộc", "invalid_credentials": "Email hoặc mật khẩu không đúng", "account_inactive": "<PERSON><PERSON><PERSON> k<PERSON>n không hoạt động", "failed": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại"}, "logout": {"success": "<PERSON><PERSON><PERSON> xuất thành công", "failed": "<PERSON><PERSON><PERSON> xuất thất bại"}, "profile": {"get_success": "<PERSON><PERSON><PERSON> thông tin người dùng thành công", "update_success": "<PERSON><PERSON><PERSON> nhật thông tin thành công", "update_failed": "<PERSON><PERSON><PERSON> nhật thông tin thất bại", "get_failed": "<PERSON><PERSON><PERSON><PERSON> thể lấy thông tin người dùng"}, "password": {"change_success": "<PERSON><PERSON><PERSON> mật kh<PERSON>u thành công", "missing_fields": "<PERSON><PERSON>t khẩu hiện tại và mật khẩu mới là bắt buộc", "invalid_current": "<PERSON><PERSON><PERSON> kh<PERSON>u hiện tại không đúng", "change_failed": "<PERSON><PERSON><PERSON> mật khẩu thất bại"}, "validation": {"invalid_email": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalid_username": "Username ph<PERSON>i có ít nhất 3 ký tự và không quá 30 ký tự", "invalid_password": "<PERSON><PERSON>t kh<PERSON>u ph<PERSON>i có ít nhất 6 ký tự", "invalid_fullname": "<PERSON><PERSON> tên không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự", "invalid_phone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}}, "payment": {"create": {"success": "<PERSON><PERSON><PERSON> l<PERSON>nh thanh toán thành công", "invalid_amount": "Số tiền phải ít nhất 1,000 VND", "invalid_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> không hợp lệ", "failed": "<PERSON><PERSON><PERSON> l<PERSON>h toán thất bại", "description": "<PERSON><PERSON><PERSON> tiền vào tài k<PERSON>n của {user}", "sepay_error": "Lỗi API Sepay"}, "callback": {"success": "<PERSON><PERSON> to<PERSON> hoàn tất thành công", "failed": "<PERSON><PERSON> <PERSON><PERSON> thất bại hoặc bị hủy", "missing_params": "<PERSON><PERSON><PERSON><PERSON> tham số bắt bu<PERSON>c", "invalid_signature": "<PERSON><PERSON> ký không hợp lệ", "payment_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch thanh toán", "callback_failed": "<PERSON><PERSON> lý callback thất b<PERSON>i", "failed_reason": "<PERSON><PERSON> <PERSON><PERSON> thất bại hoặc bị hủy"}, "webhook": {"processing_failed": "<PERSON>ử lý webhook thất bại"}, "history": {"get_failed": "<PERSON><PERSON><PERSON><PERSON> thể lấy lịch sử thanh toán"}, "details": {"get_failed": "<PERSON><PERSON><PERSON><PERSON> thể lấy chi tiết thanh toán", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch thanh toán"}}, "chat": {"message": {"required": "Tin nhắn là bắt buộc và phải là chuỗi", "success": "<PERSON><PERSON><PERSON> tin nhắn thành công", "send_success": "<PERSON><PERSON><PERSON> tin nhắn thành công", "send_failed": "<PERSON><PERSON><PERSON> tin nhắn thất bại", "process_failed": "<PERSON><PERSON> lý tin nhắn thất bại"}, "history": {"success": "<PERSON><PERSON><PERSON> lịch sử chat thành công", "get_failed": "<PERSON><PERSON><PERSON><PERSON> thể lấy lịch sử chat"}, "details": {"success": "<PERSON><PERSON><PERSON> chi tiết chat thành công", "get_failed": "<PERSON><PERSON><PERSON><PERSON> thể lấy chi tiết chat", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cuộc hội thoại"}, "create": {"success": "<PERSON><PERSON><PERSON> hội thoại thành công", "title_required": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "failed": "<PERSON><PERSON><PERSON> hội thoại thất bại"}, "update": {"success": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> cu<PERSON>c hội thoại thành công", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cuộc hội thoại", "failed": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> cu<PERSON>c hội thoại thất bại"}, "delete": {"success": "<PERSON><PERSON><PERSON> hội thoại thành công", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cuộc hội thoại", "failed": "<PERSON><PERSON><PERSON> hội thoại thất bại"}, "invalid_id": "<PERSON> cuộc hội tho<PERSON>i không hợp lệ"}, "user": {"list": {"get_failed": "<PERSON><PERSON><PERSON><PERSON> thể lấy danh sách người dùng"}}, "common": {"auth": {"token_required": "<PERSON><PERSON><PERSON> cầu token xác thực", "invalid_token": "<PERSON><PERSON> không hợp lệ", "token_expired": "To<PERSON> đã hết hạn", "user_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng", "account_inactive": "<PERSON><PERSON><PERSON> k<PERSON>n không hoạt động", "auth_failed": "<PERSON><PERSON><PERSON> thực thất bại", "insufficient_permissions": "<PERSON><PERSON><PERSON><PERSON> đủ quyền truy cập", "access_denied": "Từ chối truy cập tài nguyên này", "resource_user_id_required": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> người dùng tài nguyên"}, "validation": {"required_field": "Trư<PERSON>ng này là bắ<PERSON> buộc", "invalid_format": "<PERSON><PERSON><PERSON> dạng không hợp lệ", "min_length": "<PERSON><PERSON><PERSON> có <PERSON>t nhất {min} ký tự", "max_length": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {max} ký tự", "invalid_email": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalid_phone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}, "server": {"internal_error": "<PERSON><PERSON> xảy ra lỗi nội bộ", "route_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đường dẫn", "health_check": "<PERSON><PERSON>y chủ đang hoạt động"}, "pagination": {"invalid_page": "<PERSON><PERSON> kh<PERSON>ng h<PERSON>p lệ", "invalid_limit": "<PERSON><PERSON><PERSON><PERSON> hạn không hợp lệ"}}, "langflow": {"connection_error": "<PERSON><PERSON> lỗi, tôi đang gặp khó khăn kết nối với mô hình ngôn ngữ. <PERSON><PERSON> lòng thử lại sau.", "api_error": "Lỗi API Langflow"}, "files": {"upload": {"success": "Tải file lên thành công", "multiple_success": "<PERSON><PERSON><PERSON> n<PERSON>u file lên thành công", "no_file": "<PERSON><PERSON><PERSON><PERSON> có <PERSON> đư<PERSON>c cung cấp", "no_files": "<PERSON><PERSON><PERSON><PERSON> có file nào được cung cấp", "failed": "Tải file lên thất bại", "duplicate": "File đã tồn tại"}, "list": {"success": "<PERSON><PERSON><PERSON> danh sách file thành công", "failed": "<PERSON><PERSON><PERSON> danh s<PERSON>ch file thất bại"}, "info": {"success": "<PERSON><PERSON><PERSON> thông tin file thành công", "failed": "<PERSON><PERSON><PERSON> thông tin file thất bại"}, "stats": {"success": "<PERSON><PERSON><PERSON> thống kê file thành công", "failed": "<PERSON><PERSON><PERSON> thống kê file thất bại"}, "download": {"not_found": "Không tìm thấy file hoặc không có quyền truy cập", "failed": "Tải file xuống thất bại", "stream_error": "Lỗi khi stream file"}, "view": {"not_found": "Không tìm thấy file hoặc không có quyền truy cập", "failed": "Xem file thất bại", "stream_error": "Lỗi khi stream file"}, "update": {"success": "<PERSON><PERSON><PERSON> nh<PERSON>t file thành công", "failed": "<PERSON><PERSON><PERSON> nhật file thất bại"}, "delete": {"success": "Xóa file thành công", "not_found": "Không tìm thấy file hoặc không có quyền truy cập", "failed": "Xóa file thất bại"}, "not_found": "Không tìm thấy file hoặc không có quyền truy cập"}}