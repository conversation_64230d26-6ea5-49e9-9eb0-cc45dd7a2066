const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const mime = require('mime-types');

/**
 * Middleware xử lý file upload với Multer
 * Sử dụng memory storage để upload trực tiếp lên <PERSON>
 */

// Cấu hình storage - sử dụng memory để không lưu file tạm
const storage = multer.memoryStorage();

/**
 * File filter để kiểm tra loại file được phép upload
 * @param {Object} req - Express request object
 * @param {Object} file - File object từ multer
 * @param {Function} cb - Callback function
 */
const fileFilter = (req, file, cb) => {
  // Danh sách MIME types được phép
  const allowedTypes = [
    // Images
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/svg+xml',
    
    // Documents
    'application/pdf',
    'text/plain',
    'text/csv',
    'text/html',
    'application/json',
    'text/xml',
    'application/xml',
    
    // Microsoft Office
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    
    // Audio
    'audio/mpeg',
    'audio/wav',
    'audio/ogg',
    'audio/mp4',
    
    // Video
    'video/mp4',
    'video/mpeg',
    'video/quicktime',
    'video/x-msvideo',
    'video/webm'
  ];
  
  console.log(`📁 File upload attempt: ${file.originalname}, MIME: ${file.mimetype}`);
  
  if (allowedTypes.includes(file.mimetype)) {
    console.log(`✅ File type allowed: ${file.mimetype}`);
    cb(null, true);
  } else {
    console.log(`❌ File type not allowed: ${file.mimetype}`);
    const error = new Error(`File type '${file.mimetype}' is not allowed`);
    error.code = 'INVALID_FILE_TYPE';
    cb(error, false);
  }
};

/**
 * Cấu hình multer với các options
 */
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 50 * 1024 * 1024, // 50MB default
    files: 5, // Tối đa 5 files cùng lúc
    fields: 10 // Tối đa 10 fields khác
  }
});

/**
 * Middleware để validate file sau khi upload
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const validateFile = (req, res, next) => {
  if (!req.file && !req.files) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded',
      code: 'NO_FILE'
    });
  }
  
  // Validate single file
  if (req.file) {
    const file = req.file;
    
    // Kiểm tra file có rỗng không
    if (!file.buffer || file.buffer.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Uploaded file is empty',
        code: 'EMPTY_FILE'
      });
    }
    
    // Validate filename
    if (!file.originalname || file.originalname.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename',
        code: 'INVALID_FILENAME'
      });
    }
    
    // Kiểm tra extension có khớp với MIME type không
    const detectedMime = mime.lookup(file.originalname);
    if (detectedMime && detectedMime !== file.mimetype) {
      console.log(`⚠️ MIME type mismatch: detected ${detectedMime}, received ${file.mimetype}`);
      // Có thể log warning nhưng không block vì một số trường hợp hợp lệ
    }
    
    console.log(`✅ File validation passed: ${file.originalname} (${file.size} bytes)`);
  }
  
  // Validate multiple files
  if (req.files && Array.isArray(req.files)) {
    for (const file of req.files) {
      if (!file.buffer || file.buffer.length === 0) {
        return res.status(400).json({
          success: false,
          message: `File '${file.originalname}' is empty`,
          code: 'EMPTY_FILE'
        });
      }
    }
  }
  
  next();
};

/**
 * Error handler cho multer
 * @param {Error} error - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const handleMulterError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    console.error('Multer error:', error);
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: `File too large. Maximum size is ${Math.round((parseInt(process.env.MAX_FILE_SIZE) || 50 * 1024 * 1024) / 1024 / 1024)}MB`,
          code: 'FILE_TOO_LARGE'
        });
        
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Too many files. Maximum 5 files allowed',
          code: 'TOO_MANY_FILES'
        });
        
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: 'Unexpected field name',
          code: 'UNEXPECTED_FIELD'
        });
        
      default:
        return res.status(400).json({
          success: false,
          message: 'File upload error',
          code: 'UPLOAD_ERROR',
          details: error.message
        });
    }
  }
  
  // Custom file filter errors
  if (error.code === 'INVALID_FILE_TYPE') {
    return res.status(400).json({
      success: false,
      message: error.message,
      code: 'INVALID_FILE_TYPE'
    });
  }
  
  // Other errors
  console.error('File upload error:', error);
  return res.status(500).json({
    success: false,
    message: 'Internal server error during file upload',
    code: 'INTERNAL_ERROR'
  });
};

/**
 * Utility function để tạo filename unique
 * @param {string} originalName - Tên file gốc
 * @returns {string} - Filename mới với UUID
 */
const generateUniqueFilename = (originalName) => {
  const extension = path.extname(originalName).toLowerCase();
  const uuid = uuidv4();
  return `${uuid}${extension}`;
};

/**
 * Utility function để tạo object path trong MinIO
 * @param {string} userId - ID của user
 * @param {string} filename - Tên file
 * @param {string} category - Danh mục file (optional)
 * @returns {string} - Đường dẫn object trong bucket
 */
const generateObjectPath = (userId, filename, category = 'uploads') => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  
  return `${category}/${userId}/${year}/${month}/${filename}`;
};

module.exports = {
  upload,
  validateFile,
  handleMulterError,
  generateUniqueFilename,
  generateObjectPath
};
