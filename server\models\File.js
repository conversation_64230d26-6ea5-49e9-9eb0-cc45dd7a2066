const mongoose = require('mongoose');

/**
 * Schema cho File metadata
 * Lưu trữ thông tin về files đượ<PERSON> upload lên MinIO
 */
const fileSchema = new mongoose.Schema({
  // Tên file được tạo tự động (UUID + extension)
  filename: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Tên file gốc do user upload
  originalName: {
    type: String,
    required: true,
    maxlength: 255
  },
  
  // MIME type của file
  mimeType: {
    type: String,
    required: true,
    index: true
  },
  
  // Kích thước file (bytes)
  size: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Đường dẫn trong MinIO bucket
  path: {
    type: String,
    required: true,
    unique: true
  },
  
  // User đã upload file này
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // File có public không (cho phép access mà không cần auth)
  isPublic: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // Tags để phân loại file
  tags: [{
    type: String,
    maxlength: 50
  }],
  
  // <PERSON>ada<PERSON> bổ sung (key-value pairs)
  metadata: {
    type: Map,
    of: String,
    default: new Map()
  },
  
  // Số lần download
  downloadCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Thời gian hết hạn (optional)
  expiresAt: {
    type: Date,
    index: { expireAfterSeconds: 0 }
  },
  
  // Trạng thái file
  status: {
    type: String,
    enum: ['uploading', 'active', 'deleted', 'error'],
    default: 'active',
    index: true
  },
  
  // Thông tin bổ sung cho hình ảnh
  imageInfo: {
    width: Number,
    height: Number,
    format: String,
    hasAlpha: Boolean
  },
  
  // Hash để kiểm tra duplicate (optional)
  hash: {
    type: String,
    index: true,
    sparse: true
  }
}, {
  timestamps: true, // Tự động thêm createdAt và updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes để tối ưu query
fileSchema.index({ uploadedBy: 1, createdAt: -1 });
fileSchema.index({ uploadedBy: 1, mimeType: 1 });
fileSchema.index({ uploadedBy: 1, status: 1 });
fileSchema.index({ createdAt: -1 });
fileSchema.index({ size: 1 });

// Virtual để tạo URL download
fileSchema.virtual('downloadUrl').get(function() {
  return `/api/files/${this._id}/download`;
});

// Virtual để format size
fileSchema.virtual('formattedSize').get(function() {
  const bytes = this.size;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual để kiểm tra file type
fileSchema.virtual('fileType').get(function() {
  if (this.mimeType.startsWith('image/')) return 'image';
  if (this.mimeType.startsWith('video/')) return 'video';
  if (this.mimeType.startsWith('audio/')) return 'audio';
  if (this.mimeType === 'application/pdf') return 'pdf';
  if (this.mimeType.includes('document') || this.mimeType.includes('word')) return 'document';
  if (this.mimeType.includes('spreadsheet') || this.mimeType.includes('excel')) return 'spreadsheet';
  return 'other';
});

// Method để cập nhật download count
fileSchema.methods.incrementDownloadCount = async function() {
  this.downloadCount += 1;
  return this.save();
};

// Method để soft delete
fileSchema.methods.softDelete = async function() {
  this.status = 'deleted';
  return this.save();
};

// Static method để tìm files của user
fileSchema.statics.findByUser = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    mimeType,
    tags,
    status = 'active'
  } = options;
  
  const query = { uploadedBy: userId, status };
  
  if (mimeType) {
    query.mimeType = new RegExp(mimeType, 'i');
  }
  
  if (tags && tags.length > 0) {
    query.tags = { $in: tags };
  }
  
  const skip = (page - 1) * limit;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .select('-path'); // Không trả về MinIO path vì lý do bảo mật
};

// Static method để tính tổng dung lượng của user
fileSchema.statics.getTotalSizeByUser = function(userId) {
  return this.aggregate([
    { $match: { uploadedBy: mongoose.Types.ObjectId(userId), status: 'active' } },
    { $group: { _id: null, totalSize: { $sum: '$size' }, count: { $sum: 1 } } }
  ]);
};

// Pre-save middleware để validate
fileSchema.pre('save', function(next) {
  // Validate file size (max 50MB theo env.example)
  const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 50 * 1024 * 1024;
  if (this.size > maxSize) {
    return next(new Error(`File size exceeds maximum allowed size of ${maxSize} bytes`));
  }
  
  next();
});

// Pre-remove middleware để cleanup MinIO object (nếu cần)
fileSchema.pre('remove', async function(next) {
  // Có thể thêm logic để xóa file từ MinIO ở đây
  // const { minioClient, BUCKET_NAME } = require('../config/minio');
  // await minioClient.removeObject(BUCKET_NAME, this.path);
  next();
});

module.exports = mongoose.model('File', fileSchema);
