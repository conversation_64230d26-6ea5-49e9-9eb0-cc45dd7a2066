{"name": "chatbot-server", "version": "1.0.0", "description": "Express backend for chatbot with Langflow integration", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "minio": "^8.0.5", "mongoose": "^8.15.1", "multer": "^2.0.1", "sharp": "^0.34.2", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}