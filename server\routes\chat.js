const express = require('express');
const axios = require('axios');
const router = express.Router();
const { Chat, Message, User, File } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { upload, validateFile, handleMulterError } = require('../middleware/fileUpload');
const FileService = require('../services/fileService');
const { sendLocalizedSuccess, sendLocalizedError } = require('../utils/i18n');
const mongoose = require('mongoose');

// POST /api/chat - G<PERSON>i tin nhắn và nhận phản hồi từ Langflow
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { message, chatId } = req.body;
    
    if (!message || typeof message !== 'string') {
      return sendLocalizedError(res, 400, 'chat.message.required');
    }

    let chat;
    
    // If chatId is provided, use existing chat, otherwise create new chat
    if (chatId) {
      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(chatId)) {
        return sendLocalizedError(res, 400, 'chat.invalid_id');
      }
      
      chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
      if (!chat) {
        return sendLocalizedError(res, 404, 'chat.details.not_found');
      }
    } else {
      // Create new chat
      chat = new Chat({
        userId: req.user._id,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        type: 'general'
      });
      await chat.save();
    }

    // Create user message
    const userMessage = new Message({
      chatId: chat._id,
      sender: 'user',
      content: message,
      timestamp: new Date()
    });
    await userMessage.save();

    // Update chat analytics
    await chat.incrementMessageCount();

    // Call Langflow API
    const langflowResponse = await callLangflowAPI(message, req);
    
    // Create bot message
    const botMessage = new Message({
      chatId: chat._id,
      sender: 'bot',
      content: langflowResponse,
      timestamp: new Date()
    });
    await botMessage.save();

    // Update chat analytics again
    await chat.incrementMessageCount();

    sendLocalizedSuccess(res, 200, 'chat.message.success', {
      chatId: chat._id,
      response: langflowResponse,
      messageId: botMessage._id
    });

  } catch (error) {
    console.error('Error in chat endpoint:', error);
    sendLocalizedError(res, 500, 'chat.message.process_failed');
  }
});

// GET /api/chat/history - Lấy lịch sử các tin nhắn (danh sách chat)
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    
    const options = {
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };
    
    if (status) {
      options.status = status;
    }

    const chats = await Chat.findByUserId(req.user._id, options);
    const total = await Chat.countDocuments({ userId: req.user._id, ...(status && { status }) });

    sendLocalizedSuccess(res, 200, 'chat.history.success', {
      chats,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        hasNext: parseInt(page) * parseInt(limit) < total,
        hasPrev: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Error getting chat history:', error);
    sendLocalizedError(res, 500, 'chat.history.get_failed');
  }
});

// GET /api/chat/:chatId - Lấy chi tiết lịch sử nhắn tin gồm các message qua lại
router.get('/:chatId', authenticateToken, async (req, res) => {
  try {
    const { chatId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return sendLocalizedError(res, 400, 'chat.invalid_id');
    }

    // Verify chat belongs to user
    const chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
    if (!chat) {
      return sendLocalizedError(res, 404, 'chat.details.not_found');
    }

    // Get messages for this chat with file attachments populated
    const messages = await Message.find({ chatId })
      .populate({
        path: 'metadata.attachments.fileId',
        model: 'File',
        select: 'filename originalName mimeType size status'
      })
      .sort({ timestamp: 1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    const total = await Message.countDocuments({ chatId });

    sendLocalizedSuccess(res, 200, 'chat.details.success', {
      chat,
      messages,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        hasNext: parseInt(page) * parseInt(limit) < total,
        hasPrev: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Error getting chat details:', error);
    sendLocalizedError(res, 500, 'chat.details.get_failed');
  }
});

// POST /api/chat/create - Tạo lịch sử chat mới
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { title, type = 'general', metadata } = req.body;

    if (!title) {
      return sendLocalizedError(res, 400, 'chat.create.title_required');
    }

    const chat = new Chat({
      userId: req.user._id,
      title,
      type,
      metadata: metadata || {}
    });

    await chat.save();

    sendLocalizedSuccess(res, 201, 'chat.create.success', {
      chat
    });

  } catch (error) {
    console.error('Error creating chat:', error);
    sendLocalizedError(res, 500, 'chat.create.failed');
  }
});

// PUT /api/chat/:chatId - Cập nhật thông tin chat
router.put('/:chatId', authenticateToken, async (req, res) => {
  try {
    const { chatId } = req.params;
    const { title, status, metadata } = req.body;

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return sendLocalizedError(res, 400, 'chat.invalid_id');
    }

    const chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
    if (!chat) {
      return sendLocalizedError(res, 404, 'chat.update.not_found');
    }

    const updateData = {};
    if (title) updateData.title = title;
    if (status) updateData.status = status;
    if (metadata) updateData.metadata = { ...chat.metadata, ...metadata };

    const updatedChat = await Chat.findByIdAndUpdate(
      chatId,
      updateData,
      { new: true, runValidators: true }
    );

    sendLocalizedSuccess(res, 200, 'chat.update.success', {
      chat: updatedChat
    });

  } catch (error) {
    console.error('Error updating chat:', error);
    sendLocalizedError(res, 500, 'chat.update.failed');
  }
});

// DELETE /api/chat/:chatId - Xóa chat
router.delete('/:chatId', authenticateToken, async (req, res) => {
  try {
    const { chatId } = req.params;

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return sendLocalizedError(res, 400, 'chat.invalid_id');
    }

    const chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
    if (!chat) {
      return sendLocalizedError(res, 404, 'chat.delete.not_found');
    }

    // Delete all messages in this chat
    await Message.deleteMany({ chatId });
    
    // Delete the chat
    await Chat.findByIdAndDelete(chatId);

    sendLocalizedSuccess(res, 200, 'chat.delete.success');

  } catch (error) {
    console.error('Error deleting chat:', error);
    sendLocalizedError(res, 500, 'chat.delete.failed');
  }
});

// POST /api/chat/message - Gửi tin nhắn với mock data (giả lập AI response)
router.post('/message', authenticateToken, async (req, res) => {
  try {
    const { message, chatId } = req.body;
    
    if (!message || typeof message !== 'string') {
      return sendLocalizedError(res, 400, 'chat.message.required');
    }

    let chat;
    
    // If chatId is provided, use existing chat, otherwise create new chat
    if (chatId) {
      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(chatId)) {
        return sendLocalizedError(res, 400, 'chat.invalid_id');
      }
      
      chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
      if (!chat) {
        return sendLocalizedError(res, 404, 'chat.details.not_found');
      }
    } else {
      // Create new chat
      chat = new Chat({
        userId: req.user._id,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        type: 'general'
      });
      await chat.save();
    }

    // Create user message
    const userMessage = new Message({
      chatId: chat._id,
      sender: 'user',
      content: message,
      timestamp: new Date()
    });
    await userMessage.save();

    // Generate mock AI response with formatting
    const mockResponse = generateMockResponseWithFormat(message);
    
    // Create bot message
    const botMessage = new Message({
      chatId: chat._id,
      sender: 'bot',
      content: mockResponse,
      timestamp: new Date()
    });
    await botMessage.save();

    // Update chat analytics: tăng messageCount lên 2 (user + bot)
    await Chat.findByIdAndUpdate(chat._id, { $inc: { 'analytics.messageCount': 2 }, $set: { lastMessageAt: new Date(), updatedAt: new Date() } });

    sendLocalizedSuccess(res, 200, 'chat.message.success', {
      chatId: chat._id,
      response: mockResponse,
      messageId: botMessage._id,
      isMock: true,
      format: 'markdown'
    });

  } catch (error) {
    console.error('Error in mock message endpoint:', error);
    sendLocalizedError(res, 500, 'chat.message.process_failed');
  }
});

// POST /api/chat/:chatId/messages - Gửi tin nhắn vào chat cụ thể
router.post('/:chatId/messages', authenticateToken, async (req, res) => {
  try {
    const { chatId } = req.params;
    const { message } = req.body;

    if (!message || typeof message !== 'string') {
      return sendLocalizedError(res, 400, 'chat.message.required');
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return sendLocalizedError(res, 400, 'chat.invalid_id');
    }

    // Verify chat belongs to user
    const chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
    if (!chat) {
      return sendLocalizedError(res, 404, 'chat.details.not_found');
    }

    // Create user message
    const userMessage = new Message({
      chatId: chat._id,
      sender: 'user',
      content: message,
      timestamp: new Date()
    });
    await userMessage.save();

    // Update chat analytics
    await chat.incrementMessageCount();

    // Call Langflow API
    const langflowResponse = await callLangflowAPI(message, req);

    // Create bot message
    const botMessage = new Message({
      chatId: chat._id,
      sender: 'bot',
      content: langflowResponse,
      timestamp: new Date()
    });
    await botMessage.save();

    // Update chat analytics again
    await chat.incrementMessageCount();

    sendLocalizedSuccess(res, 200, 'chat.message.send_success', {
      userMessage,
      botMessage,
      response: langflowResponse
    });

  } catch (error) {
    console.error('Error sending message to chat:', error);
    sendLocalizedError(res, 500, 'chat.message.send_failed');
  }
});

// POST /api/chat/:chatId/messages-with-files - Gửi tin nhắn kèm files
router.post('/:chatId/messages-with-files',
  authenticateToken,
  upload.array('files', 5), // Tối đa 5 files
  handleMulterError,
  validateFile,
  async (req, res) => {
    try {
      const { chatId } = req.params;
      const { message = '' } = req.body;

      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(chatId)) {
        return sendLocalizedError(res, 400, 'chat.invalid_id');
      }

      // Verify chat belongs to user
      const chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
      if (!chat) {
        return sendLocalizedError(res, 404, 'chat.details.not_found');
      }

      // Validate that we have either message or files
      if (!message.trim() && (!req.files || req.files.length === 0)) {
        return sendLocalizedError(res, 400, 'chat.message.required');
      }

      console.log(`📤 Message with files request: chatId=${chatId}, files=${req.files?.length || 0}`);

      // Upload files first if any
      let uploadedFiles = [];
      if (req.files && req.files.length > 0) {
        console.log(`📁 Uploading ${req.files.length} files...`);

        for (const file of req.files) {
          try {
            const result = await FileService.uploadFile(
              file.buffer,
              file.originalname,
              file.mimetype,
              req.user._id,
              { category: 'chat-attachments' }
            );
            uploadedFiles.push(result.file);
            console.log(`✅ File uploaded: ${result.file.originalName}`);
          } catch (error) {
            console.error(`❌ File upload failed: ${file.originalname}`, error);
            return sendLocalizedError(res, 500, 'files.upload.failed', {
              error: `Failed to upload ${file.originalname}: ${error.message}`
            });
          }
        }
      }

      // Create user message
      const userMessage = new Message({
        chatId: chat._id,
        sender: 'user',
        content: message.trim() || `[Đã gửi ${uploadedFiles.length} file(s)]`,
        contentType: uploadedFiles.length > 0 ? 'file' : 'text',
        timestamp: new Date()
      });

      // Add file attachments if any
      if (uploadedFiles.length > 0) {
        await userMessage.addMultipleAttachments(uploadedFiles);
      } else {
        await userMessage.save();
      }

      console.log(`💾 User message saved with ${uploadedFiles.length} attachments`);

      // Update chat analytics
      await chat.incrementMessageCount();

      // Prepare message for AI (include file info)
      let aiMessage = message.trim();
      if (uploadedFiles.length > 0) {
        const fileInfo = uploadedFiles.map(f => `${f.originalName} (${f.mimeType})`).join(', ');
        aiMessage += aiMessage ? `\n\nFiles đính kèm: ${fileInfo}` : `Đã nhận files: ${fileInfo}`;
      }

      // Call Langflow API
      const langflowResponse = await callLangflowAPI(aiMessage, req);

      // Create bot message
      const botMessage = new Message({
        chatId: chat._id,
        sender: 'bot',
        content: langflowResponse,
        timestamp: new Date()
      });
      await botMessage.save();

      // Update chat analytics again
      await chat.incrementMessageCount();

      console.log(`✅ Message with files sent successfully`);

      sendLocalizedSuccess(res, 200, 'chat.message.send_success', {
        userMessage: {
          ...userMessage.toObject(),
          attachments: userMessage.metadata.attachments
        },
        botMessage,
        response: langflowResponse,
        filesUploaded: uploadedFiles.length
      });

    } catch (error) {
      console.error('Error sending message with files:', error);
      sendLocalizedError(res, 500, 'chat.message.send_failed', { error: error.message });
    }
  }
);

// POST /api/chat/stream - Stream tin nhắn trực tiếp từ Langflow
router.post('/stream', authenticateToken, async (req, res) => {
  try {
    const { message, chatId } = req.body;
    
    if (!message || typeof message !== 'string') {
      return sendLocalizedError(res, 400, 'chat.message.required');
    }

    // Validate chat if chatId provided
    let chat;
    if (chatId) {
      if (!mongoose.Types.ObjectId.isValid(chatId)) {
        return sendLocalizedError(res, 400, 'chat.invalid_id');
      }
      chat = await Chat.findOne({ _id: chatId, userId: req.user._id });
      if (!chat) {
        return sendLocalizedError(res, 404, 'chat.details.not_found');
      }
    } else {
      // Create new chat
      chat = new Chat({
        userId: req.user._id,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        type: 'general'
      });
      await chat.save();
    }

    // Save user message
    const userMessage = new Message({
      chatId: chat._id,
      sender: 'user', 
      content: message,
      timestamp: new Date()
    });
    await userMessage.save();

    // Update chat analytics atomically for user message
    await Chat.findByIdAndUpdate(chat._id, {
      $inc: { 'analytics.messageCount': 1 },
      $set: { 
        lastMessageAt: new Date(),
        updatedAt: new Date()
      }
    });

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');    // Check if Langflow is configured
    const langflowUrl = process.env.LANGFLOW_API_URL;
    const apiKey = process.env.LANGFLOW_API_KEY;

    if (!langflowUrl) {
      console.log('Langflow not configured, using mock response for streaming...');
      
      // Generate mock response for streaming
      const mockResponse = generateMockResponseWithFormat(message);
      const words = mockResponse.split(' ');
      
      // Stream mock response word by word
      for (let i = 0; i < words.length; i++) {
        const chunk = words[i] + (i < words.length - 1 ? ' ' : '');
        res.write(`data: ${JSON.stringify({ 
          chunk: chunk,
          chunkNumber: i + 1,
          timestamp: new Date().toISOString(),
          metadata: {
            source: 'mock',
            totalChunks: words.length
          }
        })}\n\n`);
        
        // Add small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      // Save complete bot message
      const botMessage = new Message({
        chatId: chat._id,
        sender: 'bot',
        content: mockResponse,
        timestamp: new Date(),
        type: 'text',
        format: 'markdown'
      });
      await botMessage.save();

      // Update chat analytics atomically for bot message
      await Chat.findByIdAndUpdate(chat._id, {
        $inc: { 'analytics.messageCount': 1 },
        $set: { 
          lastMessageAt: new Date(),
          updatedAt: new Date()
        }
      });

      // End stream
      res.write(`data: ${JSON.stringify({ 
        done: true, 
        chatId: chat._id, 
        messageId: botMessage._id,
        botMessageId: botMessage._id 
      })}\n\n`);
      res.end();
      return;
    }

    // Call Langflow API with streaming (if configured)
    const response = await axios({
      method: 'POST',
      url: langflowUrl,
      headers: {
        'Content-Type': 'application/json',
        ...(apiKey && { 'Authorization': `Bearer ${apiKey}` })
      },
      data: {
        input_value: message,
        output_type: "chat",
        input_type: "chat",
        stream: true
      },
      responseType: 'stream'
    });    // Stream response from Langflow
    let botResponse = '';
    let buffer = '';
    let chunkNumber = 0;

    response.data.on('data', (chunk) => {
      const chunkStr = chunk.toString();
      buffer += chunkStr;

      // Try to find complete JSON objects in the buffer
      let startIndex = 0;
      let endIndex = 0;
      
      while ((startIndex = buffer.indexOf('{', endIndex)) !== -1) {
        // Find the matching closing brace
        let braceCount = 1;
        endIndex = startIndex + 1;
        
        while (endIndex < buffer.length && braceCount > 0) {
          if (buffer[endIndex] === '{') braceCount++;
          if (buffer[endIndex] === '}') braceCount--;
          endIndex++;
        }

        if (braceCount === 0) {
          // We found a complete JSON object
          try {
            const jsonStr = buffer.slice(startIndex, endIndex);
            const jsonData = JSON.parse(jsonStr);
            
            // Extract message text from the complex structure
            if (jsonData.outputs?.[0]?.outputs?.[0]?.results?.message?.text) {
              const messageText = jsonData.outputs[0].outputs[0].results.message.text;
              botResponse = messageText; // Update the full response
              
              chunkNumber++;
              // Send the message text to the client
              res.write(`data: ${JSON.stringify({ 
                chunk: messageText,
                chunkNumber: chunkNumber,
                timestamp: new Date().toISOString(),
                metadata: {
                  sessionId: jsonData.session_id,
                  flowId: jsonData.outputs[0].outputs[0].results.message.flow_id,
                  source: jsonData.outputs[0].outputs[0].results.message.properties?.source
                }
              })}\n\n`);
            }
          } catch (e) {
            console.error('Error parsing JSON from Langflow:', e);
          }
        } else {
          // Incomplete JSON, wait for more data
          break;
        }
      }

      // Keep the remaining buffer for next iteration
      if (endIndex > 0) {
        buffer = buffer.slice(endIndex);
      }
    });

    response.data.on('end', async () => {
      console.log('Langflow stream ended');
      
      // Save complete bot message
      const botMessage = new Message({
        chatId: chat._id,
        sender: 'bot',
        content: botResponse || 'No response received',
        timestamp: new Date(),
        type: 'text',
        format: 'markdown'
      });
      await botMessage.save();

      // Update chat analytics atomically for bot message
      await Chat.findByIdAndUpdate(chat._id, {
        $inc: { 'analytics.messageCount': 1 },
        $set: { 
          lastMessageAt: new Date(),
          updatedAt: new Date()
        }
      });

      // End stream
      res.write(`data: ${JSON.stringify({ 
        done: true, 
        chatId: chat._id, 
        messageId: botMessage._id,
        botMessageId: botMessage._id 
      })}\n\n`);
      res.end();
    });

    response.data.on('error', (error) => {
      console.error('Langflow stream error:', error);
      res.write(`data: ${JSON.stringify({ error: 'Error processing stream from Langflow' })}\n\n`);
      res.end();
    });
  } catch (error) {
    console.error('Error in chat stream endpoint:', error);
    
    // Send error to client
    res.write(`data: ${JSON.stringify({ 
      error: 'Failed to process message: ' + error.message 
    })}\n\n`);
    res.end();
  }
});

// Function to generate mock AI responses with markdown formatting
function generateMockResponseWithFormat(userMessage) {
  const lowerMessage = userMessage.toLowerCase();
  
  // Greetings with formatting
  if (lowerMessage.includes('xin chào') || lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    return `# 👋 Xin chào!

Rất vui được gặp bạn! Tôi là **AI Assistant** của hệ thống IMTA.

## 🚀 Tôi có thể giúp bạn:

- 💬 **Trò chuyện thông minh** - Hỏi đáp mọi chủ đề
- 📚 **Học tập** - Giải thích kiến thức chi tiết  
- 💡 **Tư vấn** - Đưa ra gợi ý và lời khuyên
- 🛠️ **Hỗ trợ kỹ thuật** - Giải quyết vấn đề

Bạn muốn tôi giúp gì hôm nay? 😊`;
  }
  
  // Thanks with formatting
  if (lowerMessage.includes('cảm ơn') || lowerMessage.includes('thank')) {
    return `## 🙏 Không có gì!

Tôi rất vui được giúp bạn. 

> 💡 **Mẹo**: Bạn có thể hỏi tôi bất cứ điều gì, từ học tập đến giải trí!

Bạn có câu hỏi nào khác không? 🤔`;
  }
  
  // Goodbyes with formatting
  if (lowerMessage.includes('tạm biệt') || lowerMessage.includes('goodbye') || lowerMessage.includes('bye')) {
    return `## 👋 Tạm biệt!

Cảm ơn bạn đã trò chuyện với tôi hôm nay!

### 📝 Tóm tắt cuộc trò chuyện:
- ✅ Đã hoàn thành mục tiêu
- 🎯 Sẵn sàng hỗ trợ tiếp theo

**Hẹn gặp lại bạn!** 🌟

---
*IMTA AI Assistant - Luôn sẵn sàng phục vụ*`;
  }
  
  // AI topics with detailed formatting
  if (lowerMessage.includes('ai') || lowerMessage.includes('artificial intelligence')) {
    return `# 🤖 Trí tuệ nhân tạo (AI)

## 📖 Định nghĩa
**Trí tuệ nhân tạo (AI)** là một lĩnh vực khoa học máy tính tập trung vào việc tạo ra các hệ thống có thể thực hiện các nhiệm vụ thường yêu cầu trí thông minh của con người.

## 🧠 Các lĩnh vực chính:

### 1. **Machine Learning (ML)**
- Học từ dữ liệu
- Cải thiện hiệu suất theo thời gian
- Phân loại và dự đoán

### 2. **Deep Learning**
- Neural Networks phức tạp
- Xử lý hình ảnh và âm thanh
- Natural Language Processing

### 3. **Computer Vision**
- Nhận diện hình ảnh
- Xử lý video
- Phân tích khuôn mặt

### 4. **Natural Language Processing (NLP)**
- Hiểu và tạo ngôn ngữ tự nhiên
- Chatbots thông minh
- Dịch thuật tự động

## 💡 Ứng dụng thực tế:
- 🏥 **Y tế**: Chẩn đoán bệnh
- 🚗 **Giao thông**: Xe tự lái
- 🏦 **Tài chính**: Phát hiện gian lận
- 🎮 **Giải trí**: Game AI

> **Lưu ý**: AI đang phát triển nhanh chóng và có tiềm năng thay đổi thế giới!`;
  }
  
  // Machine Learning with formatting
  if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
    return `# 📊 Machine Learning (ML)

## 🎯 Khái niệm cơ bản

**Machine Learning** là một nhánh của AI cho phép máy tính học hỏi và cải thiện từ dữ liệu mà không cần được lập trình rõ ràng.

## 🔄 Quy trình ML:

\`\`\`
1. Thu thập dữ liệu → 2. Xử lý dữ liệu → 3. Huấn luyện mô hình → 4. Đánh giá → 5. Triển khai
\`\`\`

## 📈 Các loại Machine Learning:

### 1. **Supervised Learning** 🎯
- **Classification**: Phân loại email spam/không spam
- **Regression**: Dự đoán giá nhà

### 2. **Unsupervised Learning** 🔍
- **Clustering**: Nhóm khách hàng theo hành vi
- **Dimensionality Reduction**: Giảm chiều dữ liệu

### 3. **Reinforcement Learning** 🎮
- Học từ môi trường
- Tối ưu hóa hành động

## 🛠️ Công cụ phổ biến:
- **Python**: scikit-learn, TensorFlow, PyTorch
- **R**: caret, randomForest
- **Cloud**: AWS SageMaker, Google AI Platform

## 💼 Ứng dụng thực tế:
- 🛒 **E-commerce**: Gợi ý sản phẩm
- 📱 **Social Media**: Feed cá nhân hóa
- 🏥 **Healthcare**: Chẩn đoán bệnh
- 🏦 **Finance**: Đánh giá tín dụng

> **Pro tip**: ML cần dữ liệu chất lượng cao để hoạt động hiệu quả!`;
  }
  
  // Chatbots with formatting
  if (lowerMessage.includes('chatbot') || lowerMessage.includes('bot')) {
    return `# 🤖 Chatbot

## 📱 Định nghĩa
**Chatbot** là một ứng dụng phần mềm được thiết kế để mô phỏng cuộc trò chuyện với người dùng thông qua văn bản hoặc giọng nói.

## 🏗️ Kiến trúc Chatbot:

### 1. **Frontend Interface** 🖥️
- Web chat widget
- Mobile app integration
- Social media platforms

### 2. **Natural Language Processing** 🧠
- Intent recognition
- Entity extraction
- Sentiment analysis

### 3. **Backend Logic** ⚙️
- Business rules
- API integrations
- Database queries

### 4. **Response Generation** 💬
- Template-based responses
- Dynamic content
- Multi-language support

## 🎯 Các loại Chatbot:

| Loại | Mô tả | Ví dụ |
|------|-------|-------|
| **Rule-based** | Dựa trên quy tắc định sẵn | FAQ bot |
| **AI-powered** | Sử dụng ML/NLP | Customer service |
| **Hybrid** | Kết hợp cả hai | E-commerce assistant |

## 💼 Ứng dụng thực tế:

### 🛒 **E-commerce**
- Tư vấn sản phẩm
- Hỗ trợ thanh toán
- Theo dõi đơn hàng

### 🏥 **Healthcare**
- Đặt lịch khám
- Tư vấn sức khỏe
- Nhắc nhở thuốc

### 🏦 **Banking**
- Kiểm tra tài khoản
- Chuyển tiền
- Hỗ trợ khách hàng

## 🚀 Lợi ích:
- ⏰ **24/7 Availability**
- 💰 **Cost-effective**
- 🎯 **Consistent responses**
- 📊 **Scalable**

> **Future**: Chatbots sẽ ngày càng thông minh hơn với AI!`;
  }
  
  // General responses with formatting
  const generalResponses = [
    `## 💭 Phản hồi về: **"${userMessage}"**

Đây là một chủ đề rất thú vị! Tôi có thể chia sẻ một số thông tin hữu ích:

### 🔍 **Phân tích chủ đề:**
- Độ phức tạp: ⭐⭐⭐
- Mức độ quan tâm: 🔥🔥🔥
- Tiềm năng ứng dụng: 💡💡💡

### 📚 **Gợi ý tìm hiểu thêm:**
1. Tìm hiểu về lịch sử phát triển
2. Khám phá các ứng dụng thực tế
3. Xem xét xu hướng tương lai

> **Lưu ý**: Đây chỉ là thông tin cơ bản. Bạn có muốn tôi giải thích chi tiết hơn không?`,

    `## 🎯 Câu hỏi hay về: **"${userMessage}"**

Cảm ơn bạn đã đặt câu hỏi này! Đây là những gì tôi biết:

### 📋 **Tóm tắt:**
- **Chủ đề**: ${userMessage}
- **Phạm vi**: Rộng và đa dạng
- **Ứng dụng**: Nhiều lĩnh vực

### 🛠️ **Cách tiếp cận:**
\`\`\`
1. Hiểu vấn đề cốt lõi
2. Phân tích các khía cạnh
3. Đưa ra giải pháp
\`\`\`

### 💡 **Gợi ý:**
- Nghiên cứu thêm từ nhiều nguồn
- Thực hành để hiểu sâu hơn
- Chia sẻ kiến thức với người khác

**Bạn có muốn tôi giải thích chi tiết hơn về khía cạnh nào không?** 🤔`,

    `## 🌟 Chủ đề thú vị: **"${userMessage}"**

Tôi rất thích câu hỏi của bạn! Hãy để tôi chia sẻ:

### 🎨 **Đặc điểm nổi bật:**
- ✅ **Tính thực tiễn cao**
- ✅ **Có nhiều ứng dụng**
- ✅ **Đang phát triển nhanh**

### 📊 **Thống kê thú vị:**
- **Mức độ phổ biến**: 85%
- **Tốc độ phát triển**: 🔥🔥🔥
- **Tiềm năng tương lai**: ⭐⭐⭐⭐⭐

### 🔗 **Liên quan đến:**
- Công nghệ hiện đại
- Xu hướng thị trường
- Nhu cầu xã hội

> **Fun fact**: Chủ đề này đang được quan tâm rất nhiều trong cộng đồng! 🎉`
  ];
  
  // Return a random formatted response
  return generalResponses[Math.floor(Math.random() * generalResponses.length)];
}

// Function to call Langflow API
async function callLangflowAPI(message, req) {
  try {
    const langflowUrl = process.env.LANGFLOW_API_URL;
    const apiKey = process.env.LANGFLOW_API_KEY;
    
    if (!langflowUrl) {
      throw new Error('LANGFLOW_API_URL not configured');
    }

    const payload = {
      input_value: message,
      output_type: "chat",
      input_type: "chat"
    };

    const headers = {
      'Content-Type': 'application/json'
    };

    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }

    const response = await axios.post(langflowUrl, payload, { headers });
    
    // Handle different response formats from Langflow
    if (response.data && response.data.output) {
      return response.data.output;
    } else if (response.data && response.data.message) {
      return response.data.message;
    } else if (typeof response.data === 'string') {
      return response.data;
    } else {
      return JSON.stringify(response.data);
    }

  } catch (error) {
    console.error('Langflow API error:', error);
    
    // Return a fallback response if Langflow is not available
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return req.t('langflow.connection_error');
    }
    
    throw new Error(req.t('langflow.api_error') + ': ' + error.message);
  }
}

module.exports = router; 