const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { upload, validateFile, handleMulterError } = require('../middleware/fileUpload');
const FileService = require('../services/fileService');
const { sendLocalizedSuccess, sendLocalizedError } = require('../utils/i18n');

/**
 * Routes cho file management
 * Tất cả routes đều yêu cầu authentication
 */

// POST /api/files/upload - Upload single file
router.post('/upload', 
  authenticateToken, 
  upload.single('file'), 
  handleMulterError,
  validateFile,
  async (req, res) => {
    try {
      console.log(`📤 File upload request from user: ${req.user._id}`);
      
      if (!req.file) {
        return sendLocalizedError(res, 400, 'files.upload.no_file');
      }
      
      // Parse options từ request body
      const options = {
        isPublic: req.body.isPublic === 'true',
        tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : [],
        metadata: req.body.metadata ? JSON.parse(req.body.metadata) : {},
        category: req.body.category || 'uploads',
        checkDuplicate: req.body.checkDuplicate === 'true'
      };
      
      // Upload file
      const result = await FileService.uploadFile(
        req.file.buffer,
        req.file.originalname,
        req.file.mimetype,
        req.user._id,
        options
      );
      
      if (result.isDuplicate) {
        return sendLocalizedSuccess(res, 200, 'files.upload.duplicate', { 
          file: result.file,
          isDuplicate: true 
        });
      }
      
      sendLocalizedSuccess(res, 201, 'files.upload.success', { 
        file: result.file,
        isDuplicate: false 
      });
      
    } catch (error) {
      console.error('File upload error:', error);
      sendLocalizedError(res, 500, 'files.upload.failed', { error: error.message });
    }
  }
);

// POST /api/files/upload-multiple - Upload multiple files
router.post('/upload-multiple',
  authenticateToken,
  upload.array('files', 5), // Tối đa 5 files
  handleMulterError,
  validateFile,
  async (req, res) => {
    try {
      console.log(`📤 Multiple file upload request from user: ${req.user._id}`);
      
      if (!req.files || req.files.length === 0) {
        return sendLocalizedError(res, 400, 'files.upload.no_files');
      }
      
      // Parse options
      const options = {
        isPublic: req.body.isPublic === 'true',
        tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : [],
        metadata: req.body.metadata ? JSON.parse(req.body.metadata) : {},
        category: req.body.category || 'uploads',
        checkDuplicate: req.body.checkDuplicate === 'true'
      };
      
      // Upload tất cả files
      const uploadPromises = req.files.map(file => 
        FileService.uploadFile(
          file.buffer,
          file.originalname,
          file.mimetype,
          req.user._id,
          options
        )
      );
      
      const results = await Promise.all(uploadPromises);
      
      const uploadedFiles = results.map(result => ({
        file: result.file,
        isDuplicate: result.isDuplicate
      }));
      
      sendLocalizedSuccess(res, 201, 'files.upload.multiple_success', { 
        files: uploadedFiles,
        total: uploadedFiles.length
      });
      
    } catch (error) {
      console.error('Multiple file upload error:', error);
      sendLocalizedError(res, 500, 'files.upload.failed', { error: error.message });
    }
  }
);

// GET /api/files - Lấy danh sách files của user
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log(`📋 Get files request from user: ${req.user._id}`);
    
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      mimeType: req.query.mimeType,
      tags: req.query.tags ? req.query.tags.split(',') : undefined,
      search: req.query.search,
      sortBy: req.query.sortBy || 'createdAt',
      sortOrder: req.query.sortOrder || 'desc'
    };
    
    const result = await FileService.getUserFiles(req.user._id, options);
    
    sendLocalizedSuccess(res, 200, 'files.list.success', result);
    
  } catch (error) {
    console.error('Get files error:', error);
    sendLocalizedError(res, 500, 'files.list.failed', { error: error.message });
  }
});

// GET /api/files/stats - Lấy thống kê files của user
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    console.log(`📊 Get file stats request from user: ${req.user._id}`);
    
    const stats = await FileService.getUserFileStats(req.user._id);
    
    sendLocalizedSuccess(res, 200, 'files.stats.success', { stats });
    
  } catch (error) {
    console.error('Get file stats error:', error);
    sendLocalizedError(res, 500, 'files.stats.failed', { error: error.message });
  }
});

// GET /api/files/:id - Lấy thông tin chi tiết của file
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    console.log(`📄 Get file info request: ${req.params.id} from user: ${req.user._id}`);
    
    const File = require('../models/File');
    const file = await File.findOne({
      _id: req.params.id,
      $or: [
        { uploadedBy: req.user._id },
        { isPublic: true }
      ],
      status: 'active'
    }).select('-path -hash');
    
    if (!file) {
      return sendLocalizedError(res, 404, 'files.not_found');
    }
    
    sendLocalizedSuccess(res, 200, 'files.info.success', { file });
    
  } catch (error) {
    console.error('Get file info error:', error);
    sendLocalizedError(res, 500, 'files.info.failed', { error: error.message });
  }
});

// GET /api/files/:id/download - Download file
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    console.log(`📥 Download request: ${req.params.id} from user: ${req.user._id}`);
    
    const { stream, file } = await FileService.getFileStream(req.params.id, req.user._id);
    
    // Set headers cho download
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Length', file.size);
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalName)}"`);
    res.setHeader('X-File-Name', encodeURIComponent(file.originalName));
    res.setHeader('X-File-Size', file.size);
    
    // Pipe stream to response
    stream.pipe(res);
    
    stream.on('error', (error) => {
      console.error('Stream error:', error);
      if (!res.headersSent) {
        sendLocalizedError(res, 500, 'files.download.stream_error');
      }
    });
    
  } catch (error) {
    console.error('File download error:', error);
    if (!res.headersSent) {
      if (error.message.includes('not found')) {
        sendLocalizedError(res, 404, 'files.download.not_found');
      } else {
        sendLocalizedError(res, 500, 'files.download.failed', { error: error.message });
      }
    }
  }
});

// GET /api/files/:id/view - View file inline (không download)
router.get('/:id/view', authenticateToken, async (req, res) => {
  try {
    console.log(`👁️ View request: ${req.params.id} from user: ${req.user._id}`);
    
    const { stream, file } = await FileService.getFileStream(req.params.id, req.user._id);
    
    // Set headers cho inline view
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Length', file.size);
    res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(file.originalName)}"`);
    
    // Pipe stream to response
    stream.pipe(res);
    
    stream.on('error', (error) => {
      console.error('Stream error:', error);
      if (!res.headersSent) {
        sendLocalizedError(res, 500, 'files.view.stream_error');
      }
    });
    
  } catch (error) {
    console.error('File view error:', error);
    if (!res.headersSent) {
      if (error.message.includes('not found')) {
        sendLocalizedError(res, 404, 'files.view.not_found');
      } else {
        sendLocalizedError(res, 500, 'files.view.failed', { error: error.message });
      }
    }
  }
});

// PUT /api/files/:id - Cập nhật metadata của file
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    console.log(`✏️ Update file request: ${req.params.id} from user: ${req.user._id}`);
    
    const { tags, metadata, isPublic } = req.body;
    
    const File = require('../models/File');
    const updateData = {};
    
    if (tags !== undefined) updateData.tags = tags;
    if (metadata !== undefined) updateData.metadata = new Map(Object.entries(metadata));
    if (isPublic !== undefined) updateData.isPublic = isPublic;
    
    const file = await File.findOneAndUpdate(
      { _id: req.params.id, uploadedBy: req.user._id, status: 'active' },
      updateData,
      { new: true, runValidators: true }
    ).select('-path -hash');
    
    if (!file) {
      return sendLocalizedError(res, 404, 'files.not_found');
    }
    
    sendLocalizedSuccess(res, 200, 'files.update.success', { file });
    
  } catch (error) {
    console.error('Update file error:', error);
    sendLocalizedError(res, 500, 'files.update.failed', { error: error.message });
  }
});

// DELETE /api/files/:id - Xóa file
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    console.log(`🗑️ Delete file request: ${req.params.id} from user: ${req.user._id}`);
    
    const deletedFile = await FileService.deleteFile(req.params.id, req.user._id);
    
    sendLocalizedSuccess(res, 200, 'files.delete.success', { 
      file: {
        id: deletedFile._id,
        originalName: deletedFile.originalName
      }
    });
    
  } catch (error) {
    console.error('Delete file error:', error);
    if (error.message.includes('not found')) {
      sendLocalizedError(res, 404, 'files.delete.not_found');
    } else {
      sendLocalizedError(res, 500, 'files.delete.failed', { error: error.message });
    }
  }
});

module.exports = router;
