const { minioClient, BUCKET_NAME } = require('../config/minio');
const File = require('../models/File');
const { generateUniqueFilename, generateObjectPath } = require('../middleware/fileUpload');
const sharp = require('sharp');
const crypto = require('crypto');

/**
 * Service xử lý business logic cho file operations
 */
class FileService {
  
  /**
   * Upload file lên MinIO và lưu metadata vào MongoDB
   * @param {Buffer} fileBuffer - Buffer của file
   * @param {string} originalName - Tên file gốc
   * @param {string} mimeType - MIME type
   * @param {string} userId - ID của user upload
   * @param {Object} options - Cá<PERSON> options bổ sung
   * @returns {Promise<Object>} - File document đã được lưu
   */
  static async uploadFile(fileBuffer, originalName, mimeType, userId, options = {}) {
    try {
      console.log(`📤 Starting file upload: ${originalName} (${fileBuffer.length} bytes)`);
      
      // Tạo filename unique
      const filename = generateUniqueFilename(originalName);
      const objectPath = generateObjectPath(userId, filename, options.category);
      
      // Tạo hash để kiểm tra duplicate (optional)
      const hash = crypto.createHash('md5').update(fileBuffer).digest('hex');
      
      // Kiểm tra duplicate file (nếu enabled)
      if (options.checkDuplicate) {
        const existingFile = await File.findOne({ 
          uploadedBy: userId, 
          hash, 
          status: 'active' 
        });
        
        if (existingFile) {
          console.log(`⚠️ Duplicate file detected: ${originalName}`);
          return {
            isDuplicate: true,
            file: existingFile
          };
        }
      }
      
      // Xử lý metadata cho hình ảnh
      let imageInfo = null;
      if (mimeType.startsWith('image/')) {
        try {
          const metadata = await sharp(fileBuffer).metadata();
          imageInfo = {
            width: metadata.width,
            height: metadata.height,
            format: metadata.format,
            hasAlpha: metadata.hasAlpha || false
          };
          console.log(`🖼️ Image metadata extracted: ${imageInfo.width}x${imageInfo.height}`);
        } catch (error) {
          console.warn('Could not extract image metadata:', error.message);
        }
      }
      
      // Tạo metadata cho MinIO object
      const minioMetadata = {
        'Content-Type': mimeType,
        'X-Uploaded-By': userId,
        'X-Original-Name': encodeURIComponent(originalName),
        'X-Upload-Date': new Date().toISOString()
      };
      
      // Upload lên MinIO
      console.log(`☁️ Uploading to MinIO: ${objectPath}`);
      await minioClient.putObject(BUCKET_NAME, objectPath, fileBuffer, minioMetadata);
      console.log(`✅ MinIO upload successful: ${objectPath}`);
      
      // Tạo file document
      const fileDoc = new File({
        filename,
        originalName,
        mimeType,
        size: fileBuffer.length,
        path: objectPath,
        uploadedBy: userId,
        isPublic: options.isPublic || false,
        tags: options.tags || [],
        metadata: new Map(Object.entries(options.metadata || {})),
        imageInfo,
        hash,
        status: 'active'
      });
      
      // Lưu vào MongoDB
      await fileDoc.save();
      console.log(`💾 File metadata saved to MongoDB: ${fileDoc._id}`);
      
      return {
        isDuplicate: false,
        file: fileDoc
      };
      
    } catch (error) {
      console.error('❌ File upload error:', error);
      throw error;
    }
  }
  
  /**
   * Lấy file stream từ MinIO
   * @param {string} fileId - ID của file
   * @param {string} userId - ID của user (để kiểm tra quyền)
   * @returns {Promise<Object>} - Object chứa stream và file info
   */
  static async getFileStream(fileId, userId) {
    try {
      console.log(`📥 Getting file stream: ${fileId} for user: ${userId}`);
      
      // Tìm file trong database
      const file = await File.findOne({ 
        _id: fileId, 
        $or: [
          { uploadedBy: userId },
          { isPublic: true }
        ],
        status: 'active'
      });
      
      if (!file) {
        throw new Error('File not found or access denied');
      }
      
      // Lấy stream từ MinIO
      const stream = await minioClient.getObject(BUCKET_NAME, file.path);
      
      // Increment download count (async, không chờ)
      file.incrementDownloadCount().catch(err => {
        console.warn('Could not update download count:', err.message);
      });
      
      console.log(`✅ File stream retrieved: ${file.originalName}`);
      
      return { stream, file };
      
    } catch (error) {
      console.error('❌ Get file stream error:', error);
      throw error;
    }
  }
  
  /**
   * Xóa file từ MinIO và MongoDB
   * @param {string} fileId - ID của file
   * @param {string} userId - ID của user (để kiểm tra quyền)
   * @returns {Promise<Object>} - File đã bị xóa
   */
  static async deleteFile(fileId, userId) {
    try {
      console.log(`🗑️ Deleting file: ${fileId} for user: ${userId}`);
      
      // Tìm file trong database
      const file = await File.findOne({ 
        _id: fileId, 
        uploadedBy: userId,
        status: 'active'
      });
      
      if (!file) {
        throw new Error('File not found or access denied');
      }
      
      // Xóa từ MinIO
      await minioClient.removeObject(BUCKET_NAME, file.path);
      console.log(`☁️ File removed from MinIO: ${file.path}`);
      
      // Soft delete trong MongoDB (hoặc hard delete)
      if (process.env.SOFT_DELETE === 'true') {
        await file.softDelete();
        console.log(`💾 File soft deleted in MongoDB: ${fileId}`);
      } else {
        await File.findByIdAndDelete(fileId);
        console.log(`💾 File hard deleted in MongoDB: ${fileId}`);
      }
      
      return file;
      
    } catch (error) {
      console.error('❌ Delete file error:', error);
      throw error;
    }
  }
  
  /**
   * Lấy danh sách files của user với pagination
   * @param {string} userId - ID của user
   * @param {Object} options - Options cho query
   * @returns {Promise<Object>} - Kết quả với pagination
   */
  static async getUserFiles(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        mimeType,
        tags,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;
      
      console.log(`📋 Getting user files: ${userId}, page: ${page}, limit: ${limit}`);
      
      // Build query
      const query = { 
        uploadedBy: userId, 
        status: 'active' 
      };
      
      if (mimeType) {
        query.mimeType = new RegExp(mimeType, 'i');
      }
      
      if (tags && tags.length > 0) {
        query.tags = { $in: tags };
      }
      
      if (search) {
        query.originalName = new RegExp(search, 'i');
      }
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
      
      // Execute queries
      const [files, total] = await Promise.all([
        File.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .select('-path -hash') // Không trả về sensitive data
          .lean(),
        File.countDocuments(query)
      ]);
      
      const totalPages = Math.ceil(total / limit);
      
      console.log(`✅ Retrieved ${files.length} files (${total} total)`);
      
      return {
        files,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
      
    } catch (error) {
      console.error('❌ Get user files error:', error);
      throw error;
    }
  }
  
  /**
   * Lấy thống kê files của user
   * @param {string} userId - ID của user
   * @returns {Promise<Object>} - Thống kê
   */
  static async getUserFileStats(userId) {
    try {
      console.log(`📊 Getting file stats for user: ${userId}`);
      
      const stats = await File.aggregate([
        { $match: { uploadedBy: userId, status: 'active' } },
        {
          $group: {
            _id: null,
            totalFiles: { $sum: 1 },
            totalSize: { $sum: '$size' },
            totalDownloads: { $sum: '$downloadCount' },
            avgFileSize: { $avg: '$size' }
          }
        }
      ]);
      
      const typeStats = await File.aggregate([
        { $match: { uploadedBy: userId, status: 'active' } },
        {
          $group: {
            _id: { $substr: ['$mimeType', 0, { $indexOfCP: ['$mimeType', '/'] }] },
            count: { $sum: 1 },
            totalSize: { $sum: '$size' }
          }
        },
        { $sort: { count: -1 } }
      ]);
      
      const result = {
        totalFiles: stats[0]?.totalFiles || 0,
        totalSize: stats[0]?.totalSize || 0,
        totalDownloads: stats[0]?.totalDownloads || 0,
        avgFileSize: stats[0]?.avgFileSize || 0,
        typeBreakdown: typeStats
      };
      
      console.log(`✅ File stats retrieved for user: ${userId}`);
      return result;
      
    } catch (error) {
      console.error('❌ Get file stats error:', error);
      throw error;
    }
  }
}

module.exports = FileService;
