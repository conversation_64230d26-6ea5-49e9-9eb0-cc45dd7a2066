/**
 * Simple test for file upload endpoint
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';

async function simpleTest() {
  try {
    console.log('🧪 Testing simple file upload endpoint...');
    
    // 1. Register user first
    console.log('1. Registering user...');
    try {
      await axios.post(`${BASE_URL}/api/auth/register`, {
        username: 'testuser',
        email: '<EMAIL>',
        password: '123456',
        fullName: 'Test User'
      });
      console.log('✅ User registered');
    } catch (error) {
      console.log('⚠️ User may already exist');
    }

    // 2. Login
    console.log('2. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 2. Create test file
    const testFilePath = path.join(__dirname, 'simple-test-file.txt');
    fs.writeFileSync(testFilePath, 'Simple test file content');
    console.log('✅ Test file created');
    
    // 3. Test the simple endpoint
    console.log('3. Testing file upload...');
    const formData = new FormData();
    formData.append('message', 'Test message');
    formData.append('files', fs.createReadStream(testFilePath));
    
    const response = await axios.post(`${BASE_URL}/api/chat/test-file-upload`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      }
    });
    
    console.log('✅ Test endpoint response:', response.data);
    
    // Cleanup
    fs.unlinkSync(testFilePath);
    console.log('✅ Cleanup completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

simpleTest();
