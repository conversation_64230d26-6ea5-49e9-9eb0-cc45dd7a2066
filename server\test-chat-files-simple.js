/**
 * Simple test for chat with files
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';

async function testChatFiles() {
  try {
    console.log('🧪 Testing chat with files...');
    
    // 1. Login
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 2. Create chat
    console.log('2. Creating chat...');
    const chatResponse = await axios.post(`${BASE_URL}/api/chat/create`, {
      title: 'Test Chat with Files',
      type: 'general'
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const chatId = chatResponse.data.data.chat._id;
    console.log('✅ Chat created:', chatId);
    
    // 3. Create test file
    const testFilePath = path.join(__dirname, 'chat-test-file.txt');
    fs.writeFileSync(testFilePath, 'Test file content for chat');
    console.log('✅ Test file created');
    
    // 4. Send message with file
    console.log('3. Sending message with file...');
    const formData = new FormData();
    formData.append('message', 'Đây là tin nhắn với file đính kèm');
    formData.append('files', fs.createReadStream(testFilePath));
    
    const messageResponse = await axios.post(`${BASE_URL}/api/chat/${chatId}/messages-with-files`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      }
    });
    
    console.log('✅ Message with file sent successfully!');
    console.log('Response:', JSON.stringify(messageResponse.data, null, 2));
    
    // Cleanup
    fs.unlinkSync(testFilePath);
    console.log('✅ Cleanup completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testChatFiles();
