/**
 * Test script cho chat với file attachments
 * Chạy: node test-chat-with-files.js
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';
let authToken = '';
let chatId = '';

// Test user data
const testUser = {
  username: 'chatfileuser',
  email: '<EMAIL>',
  password: '123456',
  fullName: 'Chat File User'
};

console.log('🚀 Bắt đầu test Chat với File Attachments...\n');

async function testChatWithFiles() {
  try {
    // 1. Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log(`✅ Health Check: ${healthResponse.data.message}\n`);

    // 2. Register user (có thể fail nếu user đã tồn tại)
    console.log('2. Testing User Registration...');
    try {
      await axios.post(`${BASE_URL}/api/auth/register`, testUser);
      console.log('✅ User registered successfully\n');
    } catch (error) {
      console.log('⚠️ User registration failed (user may already exist)\n');
    }

    // 3. Login để lấy token
    console.log('3. Testing User Login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    authToken = loginResponse.data.data.token;
    console.log('✅ Login successful, token obtained\n');

    // Headers với token
    const headers = {
      'Authorization': `Bearer ${authToken}`
    };

    // 4. Tạo chat mới
    console.log('4. Creating new chat...');
    const chatResponse = await axios.post(`${BASE_URL}/api/chat/create`, {
      title: 'Test Chat with Files',
      type: 'general'
    }, { headers });
    
    chatId = chatResponse.data.data.chat._id;
    console.log(`✅ Chat created successfully. Chat ID: ${chatId}\n`);

    // 5. Tạo test file
    console.log('5. Creating test file...');
    const testFilePath = path.join(__dirname, 'chat-test-file.txt');
    const testFileContent = `Test file for chat attachment\nCreated at: ${new Date().toISOString()}\nThis file will be sent with a chat message.`;
    fs.writeFileSync(testFilePath, testFileContent);
    console.log(`✅ Test file created: ${testFilePath}\n`);

    // 6. Test gửi message với file
    console.log('6. Testing Send Message with File...');
    const formData = new FormData();
    formData.append('message', 'Đây là tin nhắn kèm theo file đính kèm. Hãy phân tích nội dung file này giúp tôi.');
    formData.append('files', fs.createReadStream(testFilePath));

    const messageResponse = await axios.post(`${BASE_URL}/api/chat/${chatId}/messages-with-files`, formData, {
      headers: {
        ...headers,
        ...formData.getHeaders()
      }
    });

    console.log(`✅ Message with file sent successfully!`);
    console.log(`   User Message ID: ${messageResponse.data.data.userMessage._id}`);
    console.log(`   Bot Message ID: ${messageResponse.data.data.botMessage._id}`);
    console.log(`   Files uploaded: ${messageResponse.data.data.filesUploaded}`);
    console.log(`   Bot Response: ${messageResponse.data.data.response.substring(0, 100)}...\n`);

    // 7. Test lấy chat history với attachments
    console.log('7. Testing Get Chat History with Attachments...');
    const historyResponse = await axios.get(`${BASE_URL}/api/chat/${chatId}`, { headers });
    
    console.log(`✅ Chat history retrieved successfully!`);
    console.log(`   Total messages: ${historyResponse.data.data.messages.length}`);
    
    // Kiểm tra message có attachments
    const messagesWithFiles = historyResponse.data.data.messages.filter(msg => 
      msg.metadata && msg.metadata.attachments && msg.metadata.attachments.length > 0
    );
    
    console.log(`   Messages with attachments: ${messagesWithFiles.length}`);
    
    if (messagesWithFiles.length > 0) {
      const firstFileMessage = messagesWithFiles[0];
      console.log(`   First attachment: ${firstFileMessage.metadata.attachments[0].originalName}`);
      console.log(`   File size: ${firstFileMessage.metadata.attachments[0].size} bytes`);
      console.log(`   Download URL: ${firstFileMessage.metadata.attachments[0].downloadUrl}\n`);
    }

    // 8. Test gửi message chỉ có file (không có text)
    console.log('8. Testing Send File Only (no text)...');
    const formData2 = new FormData();
    formData2.append('message', ''); // Empty message
    formData2.append('files', fs.createReadStream(testFilePath));

    const fileOnlyResponse = await axios.post(`${BASE_URL}/api/chat/${chatId}/messages-with-files`, formData2, {
      headers: {
        ...headers,
        ...formData2.getHeaders()
      }
    });

    console.log(`✅ File-only message sent successfully!`);
    console.log(`   User Message: ${fileOnlyResponse.data.data.userMessage.content}`);
    console.log(`   Bot Response: ${fileOnlyResponse.data.data.response.substring(0, 100)}...\n`);

    // 9. Test multiple files
    console.log('9. Testing Multiple Files...');
    const testFile2Path = path.join(__dirname, 'chat-test-file-2.txt');
    fs.writeFileSync(testFile2Path, 'Second test file content for multiple file upload test.');

    const formData3 = new FormData();
    formData3.append('message', 'Đây là tin nhắn với nhiều file đính kèm.');
    formData3.append('files', fs.createReadStream(testFilePath));
    formData3.append('files', fs.createReadStream(testFile2Path));

    const multiFileResponse = await axios.post(`${BASE_URL}/api/chat/${chatId}/messages-with-files`, formData3, {
      headers: {
        ...headers,
        ...formData3.getHeaders()
      }
    });

    console.log(`✅ Multiple files message sent successfully!`);
    console.log(`   Files uploaded: ${multiFileResponse.data.data.filesUploaded}`);
    console.log(`   Bot Response: ${multiFileResponse.data.data.response.substring(0, 100)}...\n`);

    // 10. Test final chat history
    console.log('10. Testing Final Chat History...');
    const finalHistoryResponse = await axios.get(`${BASE_URL}/api/chat/${chatId}`, { headers });
    
    console.log(`✅ Final chat history retrieved!`);
    console.log(`   Total messages: ${finalHistoryResponse.data.data.messages.length}`);
    
    const totalAttachments = finalHistoryResponse.data.data.messages.reduce((total, msg) => {
      return total + (msg.metadata?.attachments?.length || 0);
    }, 0);
    
    console.log(`   Total attachments across all messages: ${totalAttachments}\n`);

    // Cleanup
    console.log('🧹 Cleaning up...');
    fs.unlinkSync(testFilePath);
    fs.unlinkSync(testFile2Path);
    console.log('✅ Cleanup completed\n');

    console.log('🎉 Chat with Files testing completed successfully!');
    console.log('📋 Summary:');
    console.log('   ✅ Health Check');
    console.log('   ✅ User Registration/Login');
    console.log('   ✅ Chat Creation');
    console.log('   ✅ Message with File Attachment');
    console.log('   ✅ Chat History with Attachments');
    console.log('   ✅ File-only Message');
    console.log('   ✅ Multiple Files Message');
    console.log('   ✅ Final Chat History Verification');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    process.exit(1);
  }
}

// Chạy test
testChatWithFiles();
