### Test File Upload API
### Đ<PERSON><PERSON> là file test cho các API endpoints của file upload

### 1. <PERSON><PERSON><PERSON> ký user test
POST http://localhost:5001/api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "123456",
  "fullName": "Test User"
}

### 2. <PERSON><PERSON><PERSON> nhập để lấy token
POST http://localhost:5001/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

### 3. Health check
GET http://localhost:5001/api/health

### 4. Upload file (cần thay TOKEN bằng token từ login)
POST http://localhost:5001/api/files/upload
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.txt"
Content-Type: text/plain

This is a test file content for upload testing.
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="tags"

test,upload,demo
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="isPublic"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 5. Lấy danh sách files
GET http://localhost:5001/api/files
Authorization: Bearer YOUR_TOKEN_HERE

### 6. Lấy thống kê files
GET http://localhost:5001/api/files/stats
Authorization: Bearer YOUR_TOKEN_HERE

### 7. Lấy thông tin file cụ thể (cần thay FILE_ID)
GET http://localhost:5001/api/files/FILE_ID
Authorization: Bearer YOUR_TOKEN_HERE

### 8. Download file (cần thay FILE_ID)
GET http://localhost:5001/api/files/FILE_ID/download
Authorization: Bearer YOUR_TOKEN_HERE

### 9. View file inline (cần thay FILE_ID)
GET http://localhost:5001/api/files/FILE_ID/view
Authorization: Bearer YOUR_TOKEN_HERE

### 10. Cập nhật metadata file (cần thay FILE_ID)
PUT http://localhost:5001/api/files/FILE_ID
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "tags": ["updated", "test"],
  "metadata": {
    "description": "Updated test file",
    "category": "testing"
  },
  "isPublic": true
}

### 11. Xóa file (cần thay FILE_ID)
DELETE http://localhost:5001/api/files/FILE_ID
Authorization: Bearer YOUR_TOKEN_HERE

### 12. Upload multiple files
POST http://localhost:5001/api/files/upload-multiple
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="test1.txt"
Content-Type: text/plain

Test file 1 content
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="test2.txt"
Content-Type: text/plain

Test file 2 content
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="tags"

multiple,test
------WebKitFormBoundary7MA4YWxkTrZu0gW--
