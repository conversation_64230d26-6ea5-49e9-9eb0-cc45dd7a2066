# PowerShell script để test File Upload API
# Chạy script này để test tất cả endpoints

$baseUrl = "http://localhost:5001"
$testUser = @{
    username = "testuser"
    email = "<EMAIL>"
    password = "123456"
    fullName = "Test User"
}

Write-Host "🚀 Bắt đầu test File Upload API..." -ForegroundColor Green

# 1. Test Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/health" -Method GET
    Write-Host "✅ Health Check: $($healthResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ Health Check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Đăng ký user (có thể fail nếu user đã tồn tại)
Write-Host "`n2. Testing User Registration..." -ForegroundColor Yellow
try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body ($testUser | ConvertTo-Json)
    Write-Host "✅ User registered successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ User registration failed (user may already exist): $($_.Exception.Message)" -ForegroundColor Yellow
}

# 3. Đăng nhập để lấy token
Write-Host "`n3. Testing User Login..." -ForegroundColor Yellow
try {
    $loginData = @{
        email = $testUser.email
        password = $testUser.password
    }
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method POST -ContentType "application/json" -Body ($loginData | ConvertTo-Json)
    $token = $loginResponse.data.token
    Write-Host "✅ Login successful, token obtained" -ForegroundColor Green
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Headers với token
$headers = @{
    "Authorization" = "Bearer $token"
}

# 4. Tạo test file
Write-Host "`n4. Creating test file..." -ForegroundColor Yellow
$testFilePath = "test-upload.txt"
$testFileContent = "This is a test file for upload testing.`nCreated at: $(Get-Date)"
Set-Content -Path $testFilePath -Value $testFileContent
Write-Host "✅ Test file created: $testFilePath" -ForegroundColor Green

# 5. Test file upload
Write-Host "`n5. Testing File Upload..." -ForegroundColor Yellow
try {
    $form = @{
        file = Get-Item $testFilePath
        tags = "test,upload,demo"
        isPublic = "false"
        metadata = '{"description":"Test file upload","category":"testing"}'
    }
    
    $uploadResponse = Invoke-RestMethod -Uri "$baseUrl/api/files/upload" -Method POST -Headers $headers -Form $form
    $fileId = $uploadResponse.data.file._id
    Write-Host "✅ File uploaded successfully. File ID: $fileId" -ForegroundColor Green
} catch {
    Write-Host "❌ File upload failed: $($_.Exception.Message)" -ForegroundColor Red
    # Cleanup
    Remove-Item $testFilePath -ErrorAction SilentlyContinue
    exit 1
}

# 6. Test get files list
Write-Host "`n6. Testing Get Files List..." -ForegroundColor Yellow
try {
    $filesResponse = Invoke-RestMethod -Uri "$baseUrl/api/files" -Method GET -Headers $headers
    Write-Host "✅ Files list retrieved. Total files: $($filesResponse.data.pagination.total)" -ForegroundColor Green
} catch {
    Write-Host "❌ Get files list failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. Test get file stats
Write-Host "`n7. Testing Get File Stats..." -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/files/stats" -Method GET -Headers $headers
    Write-Host "✅ File stats retrieved. Total files: $($statsResponse.data.stats.totalFiles), Total size: $($statsResponse.data.stats.totalSize) bytes" -ForegroundColor Green
} catch {
    Write-Host "❌ Get file stats failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. Test get file info
Write-Host "`n8. Testing Get File Info..." -ForegroundColor Yellow
try {
    $fileInfoResponse = Invoke-RestMethod -Uri "$baseUrl/api/files/$fileId" -Method GET -Headers $headers
    Write-Host "✅ File info retrieved. File name: $($fileInfoResponse.data.file.originalName)" -ForegroundColor Green
} catch {
    Write-Host "❌ Get file info failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 9. Test file download
Write-Host "`n9. Testing File Download..." -ForegroundColor Yellow
try {
    $downloadPath = "downloaded-test.txt"
    Invoke-WebRequest -Uri "$baseUrl/api/files/$fileId/download" -Headers $headers -OutFile $downloadPath
    if (Test-Path $downloadPath) {
        $downloadedContent = Get-Content $downloadPath -Raw
        Write-Host "✅ File downloaded successfully. Content length: $($downloadedContent.Length) characters" -ForegroundColor Green
        Remove-Item $downloadPath -ErrorAction SilentlyContinue
    }
} catch {
    Write-Host "❌ File download failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 10. Test update file metadata
Write-Host "`n10. Testing Update File Metadata..." -ForegroundColor Yellow
try {
    $updateData = @{
        tags = @("updated", "test")
        metadata = @{
            description = "Updated test file"
            category = "testing-updated"
        }
        isPublic = $true
    }
    $updateResponse = Invoke-RestMethod -Uri "$baseUrl/api/files/$fileId" -Method PUT -Headers $headers -ContentType "application/json" -Body ($updateData | ConvertTo-Json)
    Write-Host "✅ File metadata updated successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Update file metadata failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 11. Test delete file
Write-Host "`n11. Testing Delete File..." -ForegroundColor Yellow
try {
    $deleteResponse = Invoke-RestMethod -Uri "$baseUrl/api/files/$fileId" -Method DELETE -Headers $headers
    Write-Host "✅ File deleted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Delete file failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Cleanup
Write-Host "`n🧹 Cleaning up..." -ForegroundColor Yellow
Remove-Item $testFilePath -ErrorAction SilentlyContinue

Write-Host "`n🎉 File Upload API testing completed!" -ForegroundColor Green
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "   - Health Check: ✅" -ForegroundColor White
Write-Host "   - User Registration: ⚠️ (may already exist)" -ForegroundColor White
Write-Host "   - User Login: ✅" -ForegroundColor White
Write-Host "   - File Upload: ✅" -ForegroundColor White
Write-Host "   - Get Files List: ✅" -ForegroundColor White
Write-Host "   - Get File Stats: ✅" -ForegroundColor White
Write-Host "   - Get File Info: ✅" -ForegroundColor White
Write-Host "   - File Download: ✅" -ForegroundColor White
Write-Host "   - Update File Metadata: ✅" -ForegroundColor White
Write-Host "   - Delete File: ✅" -ForegroundColor White
