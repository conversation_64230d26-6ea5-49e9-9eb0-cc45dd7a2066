/**
 * Test script để kiểm tra file upload fix
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';

async function testFileUploadFix() {
  try {
    console.log('🧪 Testing file upload fix...');
    
    // 1. Login
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 2. Create test PNG file
    const pngFilePath = path.join(__dirname, 'test-image.png');
    const pngContent = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
      0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 pixel
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE,
      0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54, // IDAT chunk
      0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF,
      0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82 // IEND
    ]);
    fs.writeFileSync(pngFilePath, pngContent);
    console.log('✅ Test PNG file created');
    
    // 3. Create test HTML file
    const htmlFilePath = path.join(__dirname, 'test-page.html');
    const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <title>Test HTML File</title>
</head>
<body>
    <h1>Test HTML Content</h1>
    <p>This is a test HTML file for upload testing.</p>
</body>
</html>`;
    fs.writeFileSync(htmlFilePath, htmlContent);
    console.log('✅ Test HTML file created');
    
    // 4. Test PNG upload
    console.log('2. Testing PNG file upload...');
    const pngFormData = new FormData();
    pngFormData.append('file', fs.createReadStream(pngFilePath));
    pngFormData.append('tags', 'test,png,image');
    pngFormData.append('isPublic', 'false');
    pngFormData.append('metadata', JSON.stringify({
      source: 'test',
      category: 'image'
    }));
    
    const pngResponse = await axios.post(`${BASE_URL}/api/files/upload`, pngFormData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...pngFormData.getHeaders()
      }
    });
    
    console.log('✅ PNG upload successful!');
    console.log('   File ID:', pngResponse.data.data.file._id);
    console.log('   Original Name:', pngResponse.data.data.file.originalName);
    console.log('   MIME Type:', pngResponse.data.data.file.mimeType);
    
    // 5. Test HTML upload
    console.log('3. Testing HTML file upload...');
    const htmlFormData = new FormData();
    htmlFormData.append('file', fs.createReadStream(htmlFilePath));
    htmlFormData.append('tags', 'test,html,document');
    htmlFormData.append('isPublic', 'false');
    htmlFormData.append('metadata', JSON.stringify({
      source: 'test',
      category: 'document'
    }));
    
    const htmlResponse = await axios.post(`${BASE_URL}/api/files/upload`, htmlFormData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...htmlFormData.getHeaders()
      }
    });
    
    console.log('✅ HTML upload successful!');
    console.log('   File ID:', htmlResponse.data.data.file._id);
    console.log('   Original Name:', htmlResponse.data.data.file.originalName);
    console.log('   MIME Type:', htmlResponse.data.data.file.mimeType);
    
    // 6. Test file list
    console.log('4. Testing file list...');
    const listResponse = await axios.get(`${BASE_URL}/api/files?limit=10`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ File list retrieved!');
    console.log('   Total files:', listResponse.data.data.total);
    console.log('   Files in response:', listResponse.data.data.files.length);
    
    // 7. Test file download
    console.log('5. Testing file download...');
    const downloadResponse = await axios.get(`${BASE_URL}/api/files/${pngResponse.data.data.file._id}/download`, {
      headers: { 'Authorization': `Bearer ${token}` },
      responseType: 'arraybuffer'
    });
    
    console.log('✅ File download successful!');
    console.log('   Content-Type:', downloadResponse.headers['content-type']);
    console.log('   Content-Length:', downloadResponse.headers['content-length']);
    
    // Cleanup
    console.log('🧹 Cleaning up...');
    fs.unlinkSync(pngFilePath);
    fs.unlinkSync(htmlFilePath);
    console.log('✅ Cleanup completed');
    
    console.log('\n🎉 All file upload tests passed successfully!');
    console.log('📋 Summary:');
    console.log('   ✅ PNG file upload');
    console.log('   ✅ HTML file upload');
    console.log('   ✅ File listing');
    console.log('   ✅ File download');
    console.log('\n🔧 The file upload fix is working correctly!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    
    // Cleanup on error
    try {
      const pngFilePath = path.join(__dirname, 'test-image.png');
      const htmlFilePath = path.join(__dirname, 'test-page.html');
      if (fs.existsSync(pngFilePath)) fs.unlinkSync(pngFilePath);
      if (fs.existsSync(htmlFilePath)) fs.unlinkSync(htmlFilePath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
  }
}

testFileUploadFix();
