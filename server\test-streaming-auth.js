/**
 * Test script để kiểm tra streaming authentication
 * Chạy: node test-streaming-auth.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';

async function testStreamingAuth() {
  try {
    console.log('🧪 Testing streaming authentication...');
    
    // 1. Login để lấy token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    console.log('   Token preview:', token.substring(0, 20) + '...');
    
    // 2. Test token validation
    console.log('2. Testing token validation...');
    const meResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Token validation successful');
    console.log('   User ID:', meResponse.data.data.user._id);
    console.log('   User email:', meResponse.data.data.user.email);
    console.log('   User active:', meResponse.data.data.user.status);
    
    // 3. Test regular chat endpoint (non-streaming)
    console.log('3. Testing regular chat endpoint...');
    try {
      const chatResponse = await axios.post(`${BASE_URL}/chat`, {
        message: 'Test message for authentication'
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('✅ Regular chat endpoint works');
      console.log('   Response status:', chatResponse.status);
    } catch (error) {
      console.log('❌ Regular chat endpoint failed:', error.response?.status, error.response?.data);
    }
    
    // 4. Test streaming endpoint với fetch (như frontend)
    console.log('4. Testing streaming endpoint...');
    
    const streamResponse = await fetch(`${BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        message: 'Test streaming message'
      })
    });
    
    console.log('   Stream response status:', streamResponse.status);
    console.log('   Stream response headers:', Object.fromEntries(streamResponse.headers.entries()));
    
    if (!streamResponse.ok) {
      const errorText = await streamResponse.text();
      console.log('❌ Streaming endpoint failed');
      console.log('   Error response:', errorText);
      
      // Try to parse as JSON
      try {
        const errorJson = JSON.parse(errorText);
        console.log('   Parsed error:', errorJson);
      } catch (e) {
        console.log('   Raw error text:', errorText);
      }
    } else {
      console.log('✅ Streaming endpoint accessible');
      
      // Read a few chunks to test streaming
      const reader = streamResponse.body.getReader();
      const decoder = new TextDecoder();
      let chunkCount = 0;
      
      try {
        while (chunkCount < 3) { // Read first 3 chunks
          const { done, value } = await reader.read();
          
          if (done) {
            console.log('   Stream ended early');
            break;
          }
          
          const chunk = decoder.decode(value, { stream: true });
          console.log(`   Chunk ${chunkCount + 1}:`, chunk.substring(0, 100) + '...');
          chunkCount++;
        }
        
        // Close the reader
        reader.cancel();
        console.log('✅ Streaming test completed successfully');
        
      } catch (streamError) {
        console.log('❌ Error reading stream:', streamError.message);
      }
    }
    
    // 5. Test với token invalid
    console.log('5. Testing with invalid token...');
    const invalidStreamResponse = await fetch(`${BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer invalid_token_here`
      },
      body: JSON.stringify({
        message: 'Test with invalid token'
      })
    });
    
    console.log('   Invalid token response status:', invalidStreamResponse.status);
    
    if (invalidStreamResponse.status === 401) {
      console.log('✅ Invalid token correctly rejected');
    } else {
      console.log('❌ Invalid token not properly handled');
    }
    
    // 6. Test với no token
    console.log('6. Testing with no token...');
    const noTokenResponse = await fetch(`${BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // No Authorization header
      },
      body: JSON.stringify({
        message: 'Test with no token'
      })
    });
    
    console.log('   No token response status:', noTokenResponse.status);
    
    if (noTokenResponse.status === 401) {
      console.log('✅ No token correctly rejected');
    } else {
      console.log('❌ No token not properly handled');
    }
    
    console.log('\n🎉 Streaming authentication test completed!');
    console.log('📋 Summary:');
    console.log('   ✅ Login and token generation');
    console.log('   ✅ Token validation');
    console.log('   ✅ Streaming endpoint accessibility');
    console.log('   ✅ Invalid token rejection');
    console.log('   ✅ No token rejection');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testStreamingAuth();
