/**
 * Langflow API Verification Script
 * Run this script to verify Langflow API integration before production deployment
 * 
 * Usage: node verify-langflow-api.js
 */

const axios = require('axios');
require('dotenv').config();

const LANGFLOW_URL = process.env.LANGFLOW_API_URL;
const LANGFLOW_API_KEY = process.env.LANGFLOW_API_KEY;

console.log('🔍 Langflow API Verification Script');
console.log('=====================================\n');

async function verifyLangflowAPI() {
  try {
    // 1. Check environment variables
    console.log('1. Checking Environment Variables...');
    console.log(`   LANGFLOW_API_URL: ${LANGFLOW_URL ? '✅ Set' : '❌ Not set'}`);
    console.log(`   LANGFLOW_API_KEY: ${LANGFLOW_API_KEY ? '✅ Set' : '❌ Not set'}`);
    
    if (!LANGFLOW_URL || !LANGFLOW_API_KEY) {
      console.log('\n❌ Missing required environment variables!');
      console.log('Please set LANGFLOW_API_URL and LANGFLOW_API_KEY in your .env file');
      process.exit(1);
    }
    
    console.log(`   URL: ${LANGFLOW_URL}`);
    console.log(`   API Key: ${LANGFLOW_API_KEY.substring(0, 10)}...`);
    console.log('');

    // 2. Test basic connectivity
    console.log('2. Testing Basic Connectivity...');
    try {
      const testResponse = await axios({
        method: 'POST',
        url: LANGFLOW_URL,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${LANGFLOW_API_KEY}`
        },
        data: {
          input_value: "Hello, this is a test message",
          output_type: "chat",
          input_type: "chat",
          stream: false
        },
        timeout: 30000
      });

      console.log('   ✅ Basic connectivity successful');
      console.log(`   Response status: ${testResponse.status}`);
      console.log(`   Response headers: ${JSON.stringify(testResponse.headers, null, 2)}`);
      
    } catch (connectError) {
      console.log('   ❌ Basic connectivity failed');
      console.log(`   Error: ${connectError.message}`);
      console.log(`   Status: ${connectError.response?.status}`);
      console.log(`   Status Text: ${connectError.response?.statusText}`);
      
      if (connectError.response?.data) {
        console.log(`   Response Data: ${JSON.stringify(connectError.response.data, null, 2)}`);
      }
      
      throw connectError;
    }

    // 3. Test streaming functionality
    console.log('\n3. Testing Streaming Functionality...');
    try {
      const streamResponse = await axios({
        method: 'POST',
        url: LANGFLOW_URL,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${LANGFLOW_API_KEY}`
        },
        data: {
          input_value: "Test streaming response",
          output_type: "chat",
          input_type: "chat",
          stream: true
        },
        responseType: 'stream',
        timeout: 30000
      });

      console.log('   ✅ Streaming request initiated successfully');
      console.log(`   Response status: ${streamResponse.status}`);
      
      // Read first few chunks to verify streaming works
      let chunkCount = 0;
      const maxChunks = 3;
      
      return new Promise((resolve, reject) => {
        streamResponse.data.on('data', (chunk) => {
          chunkCount++;
          console.log(`   📦 Chunk ${chunkCount}: ${chunk.toString().substring(0, 100)}...`);
          
          if (chunkCount >= maxChunks) {
            console.log('   ✅ Streaming functionality verified');
            streamResponse.data.destroy(); // Close the stream
            resolve();
          }
        });

        streamResponse.data.on('end', () => {
          console.log('   ✅ Stream ended successfully');
          resolve();
        });

        streamResponse.data.on('error', (error) => {
          console.log('   ❌ Stream error:', error.message);
          reject(error);
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          if (chunkCount === 0) {
            console.log('   ⚠️ No chunks received within timeout');
            streamResponse.data.destroy();
            resolve();
          }
        }, 10000);
      });

    } catch (streamError) {
      console.log('   ❌ Streaming test failed');
      console.log(`   Error: ${streamError.message}`);
      throw streamError;
    }

  } catch (error) {
    console.log('\n❌ Langflow API Verification Failed!');
    console.log(`Error: ${error.message}`);
    
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Status Text: ${error.response.statusText}`);
      
      if (error.response.data) {
        console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }
    
    console.log('\n🔧 Troubleshooting Steps:');
    console.log('1. Verify your Langflow API URL is correct');
    console.log('2. Check if your API key is valid and not expired');
    console.log('3. Ensure your Langflow flow is published and accessible');
    console.log('4. Check if there are any rate limits or IP restrictions');
    console.log('5. Test the API directly using curl or Postman');
    
    process.exit(1);
  }
}

async function runVerification() {
  try {
    await verifyLangflowAPI();
    
    console.log('\n🎉 Langflow API Verification Completed Successfully!');
    console.log('=====================================');
    console.log('✅ Environment variables configured');
    console.log('✅ Basic connectivity working');
    console.log('✅ Streaming functionality working');
    console.log('✅ Ready for production deployment');
    console.log('\n🚀 You can now deploy to production with confidence!');
    
  } catch (error) {
    console.log('\n💥 Verification failed. Please fix the issues before deploying to production.');
    process.exit(1);
  }
}

// Run the verification
runVerification();
