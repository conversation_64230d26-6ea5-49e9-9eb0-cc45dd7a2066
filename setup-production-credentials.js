#!/usr/bin/env node

/**
 * Production Credentials Setup Script
 * 
 * This script helps you configure valid Langflow API credentials for production deployment.
 * 
 * Usage:
 *   node setup-production-credentials.js
 *   node setup-production-credentials.js --url YOUR_URL --key YOUR_KEY
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupCredentials() {
  console.log('🚀 IMTA AI - Production Credentials Setup');
  console.log('=========================================\n');

  // Check for command line arguments
  const args = process.argv.slice(2);
  let langflowUrl = '';
  let langflowKey = '';

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--url' && args[i + 1]) {
      langflowUrl = args[i + 1];
      i++;
    } else if (args[i] === '--key' && args[i + 1]) {
      langflowKey = args[i + 1];
      i++;
    }
  }

  // Interactive input if not provided via command line
  if (!langflowUrl) {
    console.log('📝 Please provide your Langflow API credentials:\n');
    console.log('Example URL: https://langflow.mecode.pro/api/v1/run/your-flow-id');
    langflowUrl = await question('Enter your Langflow API URL: ');
  }

  if (!langflowKey) {
    console.log('\nExample Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx');
    langflowKey = await question('Enter your Langflow API Key: ');
  }

  rl.close();

  // Validate inputs
  if (!langflowUrl || !langflowKey) {
    console.log('❌ Error: Both URL and API key are required!');
    process.exit(1);
  }

  if (!langflowUrl.startsWith('http')) {
    console.log('❌ Error: URL must start with http:// or https://');
    process.exit(1);
  }

  if (!langflowKey.startsWith('sk-')) {
    console.log('⚠️ Warning: API key should typically start with "sk-"');
    const confirm = await question('Continue anyway? (y/N): ');
    if (confirm.toLowerCase() !== 'y') {
      process.exit(1);
    }
  }

  console.log('\n🔧 Updating configuration files...\n');

  try {
    // Update server/.env
    const envPath = path.join(__dirname, 'server', '.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent = envContent.replace(
      /LANGFLOW_API_URL=.*/,
      `LANGFLOW_API_URL=${langflowUrl}`
    );
    envContent = envContent.replace(
      /LANGFLOW_API_KEY=.*/,
      `LANGFLOW_API_KEY=${langflowKey}`
    );
    
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Updated server/.env');

    // Update server/.env.production
    const envProdPath = path.join(__dirname, 'server', '.env.production');
    if (fs.existsSync(envProdPath)) {
      let envProdContent = fs.readFileSync(envProdPath, 'utf8');
      
      envProdContent = envProdContent.replace(
        /LANGFLOW_API_URL=.*/,
        `LANGFLOW_API_URL=${langflowUrl}`
      );
      envProdContent = envProdContent.replace(
        /LANGFLOW_API_KEY=.*/,
        `LANGFLOW_API_KEY=${langflowKey}`
      );
      
      fs.writeFileSync(envProdPath, envProdContent);
      console.log('✅ Updated server/.env.production');
    }

    // Update docker-compose.prod.yml
    const dockerComposePath = path.join(__dirname, 'docker-compose.prod.yml');
    if (fs.existsSync(dockerComposePath)) {
      let dockerContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      dockerContent = dockerContent.replace(
        /- LANGFLOW_API_URL=.*/,
        `      - LANGFLOW_API_URL=${langflowUrl}`
      );
      dockerContent = dockerContent.replace(
        /- LANGFLOW_API_KEY=.*/,
        `      - LANGFLOW_API_KEY=${langflowKey}`
      );
      
      fs.writeFileSync(dockerComposePath, dockerContent);
      console.log('✅ Updated docker-compose.prod.yml');
    }

    console.log('\n🎉 Configuration updated successfully!\n');
    
    console.log('📋 Next Steps:');
    console.log('1. Verify your credentials:');
    console.log('   cd server && node verify-langflow-api.js');
    console.log('');
    console.log('2. Deploy to production:');
    console.log('   docker-compose -f docker-compose.prod.yml up -d --build');
    console.log('');
    console.log('3. Check deployment status:');
    console.log('   docker-compose -f docker-compose.prod.yml logs -f app');
    console.log('');

  } catch (error) {
    console.log('❌ Error updating configuration files:', error.message);
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user');
  process.exit(0);
});

// Run the setup
setupCredentials().catch((error) => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
