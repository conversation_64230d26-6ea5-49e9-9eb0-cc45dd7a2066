/**
 * Test script để kiểm tra frontend API fix
 * Chạy: node test-frontend-api-fix.js
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001/api';

async function testFrontendAPIFix() {
  try {
    console.log('🧪 Testing frontend API fix...');
    
    // 1. Login
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    console.log('   Response structure:', {
      success: loginResponse.data.success,
      hasData: !!loginResponse.data.data,
      hasToken: !!loginResponse.data.data.token
    });
    
    // 2. Create test files
    const pngFilePath = path.join(__dirname, 'test-frontend-image.png');
    const pngContent = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
      0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE,
      0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54,
      0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF,
      0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    fs.writeFileSync(pngFilePath, pngContent);
    console.log('✅ Test PNG file created');
    
    // 3. Test upload với response structure mới
    console.log('2. Testing upload with new response structure...');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(pngFilePath));
    formData.append('tags', 'test,frontend,png');
    formData.append('isPublic', 'false');
    formData.append('metadata', JSON.stringify({
      source: 'frontend-api-test',
      category: 'image'
    }));
    
    const uploadResponse = await axios.post(`${BASE_URL}/files/upload`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      }
    });
    
    console.log('✅ Upload successful!');
    console.log('   Response structure analysis:');
    console.log('   - success:', uploadResponse.data.success);
    console.log('   - message:', uploadResponse.data.message);
    console.log('   - data.file exists:', !!uploadResponse.data.data?.file);
    console.log('   - data.file.id:', uploadResponse.data.data?.file?._id);
    console.log('   - data.file.originalName:', uploadResponse.data.data?.file?.originalName);
    
    // 4. Test với logic frontend mới
    console.log('3. Testing frontend logic simulation...');
    const response = uploadResponse.data;
    
    // Simulate frontend logic
    if (response.success) {
      const serverFile = response.data.file;
      console.log('✅ Frontend logic would succeed!');
      console.log('   - File ID:', serverFile._id);
      console.log('   - Original Name:', serverFile.originalName);
      console.log('   - Size:', serverFile.size);
      console.log('   - MIME Type:', serverFile.mimeType);
    } else {
      console.log('❌ Frontend logic would fail!');
      console.log('   - Error message:', response.message);
    }
    
    // 5. Test file list
    console.log('4. Testing file list...');
    const listResponse = await axios.get(`${BASE_URL}/files?limit=5`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ File list retrieved!');
    console.log('   Response structure analysis:');
    console.log('   - success:', listResponse.data.success);
    console.log('   - data.files exists:', !!listResponse.data.data?.files);
    console.log('   - files count:', listResponse.data.data?.files?.length || 0);
    
    // Simulate frontend logic for file list
    const listResponseData = listResponse.data;
    if (listResponseData.success) {
      const serverFiles = listResponseData.data.files;
      console.log('✅ Frontend file list logic would succeed!');
      console.log('   - Files loaded:', serverFiles.length);
    } else {
      console.log('❌ Frontend file list logic would fail!');
    }
    
    // Cleanup
    console.log('🧹 Cleaning up...');
    fs.unlinkSync(pngFilePath);
    console.log('✅ Cleanup completed');
    
    console.log('\n🎉 Frontend API fix verification completed!');
    console.log('📋 Summary:');
    console.log('   ✅ Login response structure correct');
    console.log('   ✅ Upload response structure correct');
    console.log('   ✅ Frontend logic simulation successful');
    console.log('   ✅ File list response structure correct');
    console.log('\n🔧 The frontend-backend synchronization issue is FIXED!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    
    // Cleanup on error
    try {
      const pngFilePath = path.join(__dirname, 'test-frontend-image.png');
      if (fs.existsSync(pngFilePath)) fs.unlinkSync(pngFilePath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
  }
}

testFrontendAPIFix();
